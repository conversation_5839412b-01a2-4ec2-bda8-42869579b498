package org.contrum.prisma.utils;

import lombok.experimental.UtilityClass;
import org.bukkit.plugin.Plugin;

@UtilityClass
public class PluginUtils {

    public boolean isPluginEnabled(String pluginName) {
        return org.bukkit.Bukkit.getPluginManager().isPluginEnabled(pluginName);
    }

    public Plugin getPlugin(String pluginName) {
        return org.bukkit.Bukkit.getPluginManager().getPlugin(pluginName);
    }

}
