package org.contrum.prisma.utils.timer;

import lombok.Getter;
import lombok.Setter;
import org.bson.Document;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.serialize.DocumentSerialized;

import java.time.Duration;
import java.util.UUID;

@Getter @Setter
public abstract class Timer implements DocumentSerialized {

    private final PaperServices services;

    private long remainingTime;
    private boolean cancelled;

    public Timer(PaperServices services, Document document) {
        this.services = services;

        if (document.containsKey("remainingTime"))
            this.remainingTime = document.get("remainingTime", Number.class).longValue();
        else if (document.containsKey("endAt"))
            this.remainingTime = document.get("endAt", Number.class).longValue() - System.currentTimeMillis();
    }

    public Timer(PaperServices services, Duration duration) {
        this.services = services;
        this.remainingTime = duration.toMillis();
    }

    public abstract String getDisplayName();

    public boolean isExpired() {
        return cancelled || remainingTime <= 0;
    }

    public long getRemaining() {
        return remainingTime;
    }

    public void cancel() {
        this.remainingTime = 0;
        this.cancelled = true;
    }

    @Override
    public Document serialize() {
        return new Document()
                .append("remainingTime", remainingTime);
    }
}
