package org.contrum.prisma.utils.menus.geyser.wrappers.customform.items.impl;

import lombok.SneakyThrows;
import org.checkerframework.checker.index.qual.Positive;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.common.returnsreceiver.qual.This;
import org.contrum.prisma.utils.menus.geyser.wrappers.FormWrapper;
import org.contrum.prisma.utils.menus.geyser.wrappers.customform.items.CustomFormResponsibleItem;
import org.geysermc.cumulus.component.SliderComponent;
import org.geysermc.cumulus.component.ToggleComponent;
import org.geysermc.cumulus.form.CustomForm;
import org.geysermc.cumulus.form.impl.FormImpl;
import org.geysermc.cumulus.response.CustomFormResponse;

import java.lang.reflect.Method;

public class SliderFormItem extends CustomFormResponsibleItem<SliderComponent, Float> {

    public SliderFormItem(SliderComponent component) {
        super(component);
    }

    @Override
    protected Float getResult(CustomFormResponse response, int index) {
        return response.asSlider(index);
    }

    @Override
    public void apply(CustomForm.Builder builder) {
        builder.component(super.getValue());
    }

    public static SliderFormItem of(SliderComponent component) {
        return new SliderFormItem(component);
    }

    @SneakyThrows
    public static SliderFormItem of(FormWrapper wrapper, @NonNull String text, float min, float max, @Positive float step, float defaultValue) {
        Method method = FormImpl.Builder.class.getDeclaredMethod("translate", String.class);
        method.setAccessible(true);

        return new SliderFormItem(SliderComponent.of(
                (String) method.invoke(wrapper.getBuilder(), text),
                min, max, step, defaultValue
        ));
    }

    @SneakyThrows
    public static SliderFormItem of(FormWrapper wrapper, @NonNull String text, float min, float max, @Positive float step) {
        Method method = FormImpl.Builder.class.getDeclaredMethod("translate", String.class);
        method.setAccessible(true);

        return new SliderFormItem(SliderComponent.of(
                (String) method.invoke(wrapper.getBuilder(), text),
                min, max, step
        ));
    }

    @SneakyThrows
    public static SliderFormItem of(FormWrapper wrapper, @NonNull String text, float min, float max) {
        Method method = FormImpl.Builder.class.getDeclaredMethod("translate", String.class);
        method.setAccessible(true);

        return new SliderFormItem(SliderComponent.of(
                (String) method.invoke(wrapper.getBuilder(), text),
                min, max
        ));
    }
}
