package org.contrum.prisma.utils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

public class FinalFieldUtil {

    public static void setFieldAccessible(Field field) throws Exception {
        field.setAccessible(true);
        Method getDeclaredFields0 = Class.class.getDeclaredMethod("getDeclaredFields0", boolean.class);
        getDeclaredFields0.setAccessible(true);
        Field[] fields = (Field[]) getDeclaredFields0.invoke(Field.class, false);
        for (Field each : fields) {
            if ("modifiers".equals(each.getName())) {
                each.setAccessible(true);
                each.setInt(field, field.getModifiers() & ~Modifier.FINAL);
                break;
            }
        }
    }

}