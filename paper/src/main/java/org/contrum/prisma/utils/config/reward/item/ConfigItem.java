package org.contrum.prisma.utils.config.reward.item;

import com.mojang.authlib.GameProfile;
import com.mojang.authlib.properties.Property;
import lombok.*;
import org.apache.commons.codec.binary.Base64;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.persistence.PersistentDataType;
import org.contrum.prisma.utils.ItemBuilder1_20;

import javax.annotation.Nullable;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.Set;

@NoArgsConstructor
@AllArgsConstructor
@Getter
public class ConfigItem {
    @Setter(AccessLevel.PROTECTED)
    private ItemStack item;

    public ConfigItem(ConfigurationSection section) {
        this(section, null);
    }

    public ConfigItem(ConfigurationSection section, @Nullable Player player) {
        if (!section.contains("MATERIAL")) {
            this.item = null;
            return;
        }

        Material material = Material.valueOf(section.getString("MATERIAL", "STONE").toUpperCase());
        String name = section.getString("NAME", "");
        List<String> lore = section.getStringList("LORE");
        boolean hideInfo = section.getBoolean("HIDE_ALL", false);

        ItemBuilder1_20 builder = new ItemBuilder1_20(material);
        if (!name.isEmpty()) builder.name(name);
        builder.setLore(lore);

        ConfigurationSection enchantsSection = section.getConfigurationSection("ENCHANTMENTS");
        if (enchantsSection != null){
            for (String id : enchantsSection.getKeys(false)) {
                Enchantment enchantment = Enchantment.getByName(id);
                int level = enchantsSection.getInt(id, 1);
                if (enchantment == null) {
                    Bukkit.getLogger().warning("Enchantment '" + id + "' doesn't exist!");
                    continue;
                }

                builder.addUnsafeEnchantment(enchantment, level);
            }
        }

        String owner = section.getString("OWNER");
        if (owner != null && material == Material.PLAYER_HEAD) {

            if (player != null && owner.equals("<player_name>"))
                builder.setOwnerUUID(player.getUniqueId());
            else if (owner.startsWith("http"))
                builder.setOwnerUrl(owner);
            else
                builder.setOwner(owner);
        }

        List<String> tags = section.getStringList("TAGS");
        for (String tag : tags) {
            builder.addNBT(tag, PersistentDataType.BOOLEAN, true);
        }
        builder.unbreakable(section.getBoolean("UNBREAKABLE", false));

        if (hideInfo) builder.hideInformation();
        this.item = builder.build();
    }

    public void serialize(ConfigurationSection section) {
        if (item == null) return;

        section.set("MATERIAL", item.getType().name());
        if (item.hasDisplayName()) {
            section.set("NAME", item.getItemMeta().getDisplayName());
        }

        if (item.hasLore()) {
            section.set("LORE", item.getItemMeta().getLore());
        }

        if (item.hasItemMeta()) {
            ItemMeta meta = item.getItemMeta();

            Set<ItemFlag> flags = meta.getItemFlags();
            if (flags.contains(ItemFlag.HIDE_ATTRIBUTES)) {
                section.set("HIDE_ALL", true);
            }

            // Idk if this is rght xD
            for (NamespacedKey key : meta.getPersistentDataContainer().getKeys()) {
                if (meta.getPersistentDataContainer().has(key, PersistentDataType.BOOLEAN)) {
                    section.set("TAGS." + key.getKey(), meta.getPersistentDataContainer().get(key, PersistentDataType.BOOLEAN));
                } else if (meta.getPersistentDataContainer().has(key, PersistentDataType.STRING)) {
                    section.set("TAGS." + key.getKey(), meta.getPersistentDataContainer().get(key, PersistentDataType.STRING));
                } else if (meta.getPersistentDataContainer().has(key, PersistentDataType.INTEGER)) {
                    section.set("TAGS." + key.getKey(), meta.getPersistentDataContainer().get(key, PersistentDataType.INTEGER));
                }
            }

            if (item.getType() == Material.PLAYER_HEAD) {
                SkullMeta skullMeta = (SkullMeta) item.getItemMeta();

                boolean found = false;
                if (skullMeta.getOwningPlayer() != null && skullMeta.getOwningPlayer().getName() != null) {
                    section.set("OWNER", skullMeta.getOwningPlayer().getName());
                    found = true;
                } else if (skullMeta.getPlayerProfile() != null && skullMeta.getPlayerProfile().getTextures() != null) {
                    try {
                        Field profileField = meta.getClass().getDeclaredField("profile");
                        profileField.setAccessible(true);
                        Object profile = profileField.get(meta);

                        if (profile instanceof GameProfile) {
                            GameProfile gameProfile = (GameProfile) profile;
                            Property textures = (Property) gameProfile.getProperties().get("textures").stream().findFirst().orElse(null);
                            if (textures != null) {
                                String textureData = new String(Base64.decodeBase64(textures.getValue()));
                                int urlStart = textureData.indexOf("\"url\":\"") + 7;
                                int urlEnd = textureData.indexOf("\"", urlStart);

                                section.set("OWNER", textureData.substring(urlStart, urlEnd));
                                found = true;
                            }
                        }
                    } catch (NoSuchFieldException | IllegalAccessException ignored) {
                    }
                }

                if (!found && skullMeta.getOwner() != null) {
                    section.set("OWNER", skullMeta.getOwner());
                }
            }
        }

        if (item.hasEnchants()) {
            for (Map.Entry<Enchantment, Integer> entry : item.getEnchantments().entrySet()) {
                Enchantment enchant = entry.getKey();
                int level = entry.getValue();

                section.set("ENCHANTMENTS." + enchant.getName(), level);
            }
        }

        section.set("UNBREAKABLE", item.getItemMeta().isUnbreakable());
    }
}
