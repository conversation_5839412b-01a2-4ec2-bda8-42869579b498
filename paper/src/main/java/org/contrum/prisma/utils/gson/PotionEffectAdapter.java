package org.contrum.prisma.utils.gson;

import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParseException;
import com.google.gson.JsonSerializationContext;
import com.google.gson.JsonSerializer;
import java.lang.reflect.Type;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

public class PotionEffectAdapter implements JsonDeserializer<PotionEffect>, JsonSerializer<PotionEffect> {
    public JsonElement serialize(PotionEffect src, Type typeOfSrc, JsonSerializationContext context) {
        return toJson(src);
    }

    public PotionEffect deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
        return fromJson(json);
    }

    public static JsonObject toJson(PotionEffect potionEffect) {
        if (potionEffect == null) {
            return null;
        }
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("id", Integer.valueOf(potionEffect.getType().getId()));
        jsonObject.addProperty("duration", Integer.valueOf(potionEffect.getDuration()));
        jsonObject.addProperty("amplifier", Integer.valueOf(potionEffect.getAmplifier()));
        jsonObject.addProperty("ambient", Boolean.valueOf(potionEffect.isAmbient()));
        return jsonObject;
    }

    public static PotionEffect fromJson(JsonElement jsonElement) {
        if (jsonElement == null || !jsonElement.isJsonObject()) {
            return null;
        }
        JsonObject jsonObject = jsonElement.getAsJsonObject();
        if (jsonObject.get("id") == null) {
            return null;
        }
        PotionEffectType effectType = PotionEffectType.getById(jsonObject.get("id").getAsInt());
        int duration = jsonObject.get("duration").getAsInt();
        int amplifier = jsonObject.get("amplifier").getAsInt();
        boolean ambient = jsonObject.get("ambient").getAsBoolean();
        return new PotionEffect(effectType, duration, amplifier, ambient);
    }
}
