/*
 *  This file is part of the Apple Core project.
 *  Copyright (c) 2022-2024. Contrum Services
 *  Created by izLoki on 20/06/2024
 *  Website: contrum.org
 */

package org.contrum.prisma.utils.gson;

import com.google.gson.*;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.customnpcs.npcs.seller.SellerItem;

import java.lang.reflect.Type;
import java.util.Base64;

public class Seller<PERSON>temAdapter implements JsonSerializer<SellerItem>, JsonDeserializer<SellerItem> {

    @Override
    public JsonElement serialize(SellerItem src, Type typeOfSrc, JsonSerializationContext context) {
        return toJson(src);
    }

    @Override
    public SellerItem deserialize(JsonElement jsonElement, Type type, JsonDeserializationContext context) throws JsonParseException {
        return fromJson(jsonElement);
    }


    public static JsonObject toJson(SellerItem item) {
        JsonObject jsonObject = new JsonObject();
        jsonObject.addProperty("item", new JsonPrimitive(Base64.getEncoder().encodeToString(item.getItem().serializeAsBytes())).getAsString());
        jsonObject.addProperty("cost", item.getCost());
        return jsonObject;
    }

    public static SellerItem fromJson(JsonElement jsonElement) {
        JsonObject jsonObject = jsonElement.getAsJsonObject();


        ItemStack item = ItemStack.deserializeBytes(Base64.getDecoder().decode(jsonObject.get("item").getAsString()));
        int cost = jsonObject.get("cost").getAsInt();

        return new SellerItem(item, cost);
    }
}