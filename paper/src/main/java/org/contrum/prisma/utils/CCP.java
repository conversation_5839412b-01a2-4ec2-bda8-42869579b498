/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 17/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.utils;

import org.bukkit.ChatColor;

import java.util.List;
import java.util.stream.Collectors;

public class CCP {
    public static String translate(String message){
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    public static String translateAndFront(String message){
        return ChatColor.translateAlternateColorCodes('&', prismaFont(message));
    }

    public static List<String> translateAndFront(List<String> toTranslate){
        return toTranslate.stream().map(CCP::translateAndFront).collect(Collectors.toList());
    }

    public static List<String> translate(List<String> toTranslate) {
        return toTranslate.stream().map(CCP::translate).collect(Collectors.toList());
    }

    private static String prismaFont(String string) {
        char[][] font = {
                {'a', 'ᴀ'}, {'b', 'ʙ'}, {'c', 'ᴄ'}, {'d', 'ᴅ'}, {'e', 'ᴇ'},
                {'f', 'ꜰ'}, {'g', 'ɢ'}, {'h', 'ʜ'}, {'i', 'ɪ'}, {'j', 'ᴊ'},
                {'k', 'ᴋ'}, {'l', 'ʟ'}, {'m', 'ᴍ'}, {'n', 'ɴ'}, {'ñ', 'ñ'},
                {'o', 'ᴏ'}, {'p', 'ᴘ'}, {'q', 'q'}, {'r', 'ʀ'}, {'s', 's'},
                {'t', 'ᴛ'}, {'u', 'ᴜ'}, {'v', 'ᴠ'}, {'w', 'ᴡ'}, {'x', 'x'},
                {'y', 'ʏ'}, {'z', 'ᴢ'}
        };

        StringBuilder newString = new StringBuilder();
        char[] characters = string.toCharArray();

        for (int i = 0; i < characters.length; i++) {
            char letra = characters[i];

            if (i > 0 && characters[i - 1] == '&') {
                newString.append(letra);
                continue;
            }

            boolean found = false;
            for (char[] par : font) {
                if (par[0] == letra) {
                    newString.append(par[1]);
                    found = true;
                    break;
                }
            }

            if (!found) {
                newString.append(letra);
            }
        }

        return newString.toString();
    }

    public static String stripPrismaFont(String string, boolean capitalize) {
        char[][] font = {
                {'a', 'ᴀ'}, {'b', 'ʙ'}, {'c', 'ᴄ'}, {'d', 'ᴅ'}, {'e', 'ᴇ'},
                {'f', 'ꜰ'}, {'g', 'ɢ'}, {'h', 'ʜ'}, {'i', 'ɪ'}, {'j', 'ᴊ'},
                {'k', 'ᴋ'}, {'l', 'ʟ'}, {'m', 'ᴍ'}, {'n', 'ɴ'}, {'ñ', 'ñ'},
                {'o', 'ᴏ'}, {'p', 'ᴘ'}, {'q', 'q'}, {'r', 'ʀ'}, {'s', 's'},
                {'t', 'ᴛ'}, {'u', 'ᴜ'}, {'v', 'ᴠ'}, {'w', 'ᴡ'}, {'x', 'x'},
                {'y', 'ʏ'}, {'z', 'ᴢ'}
        };
        StringBuilder originalString = new StringBuilder();
        char[] characters = string.toCharArray();

        for (char letra : characters) {
            boolean found = false;
            for (char[] par : font) {
                if (par[1] == letra) {
                    originalString.append(par[0]);
                    found = true;
                    break;
                }
            }

            if (!found) {
                originalString.append(letra);
            }
        }

        String result = originalString.toString();
        return capitalize ? capitalizeFirstLetter(result) : result;
    }

    private static String capitalizeFirstLetter(String text) {
        if (text == null || text.isEmpty()) {
            return text;
        }
        return Character.toUpperCase(text.charAt(0)) + text.substring(1);
    }
}
