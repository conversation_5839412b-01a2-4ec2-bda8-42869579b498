package org.contrum.prisma.utils;

import com.sk89q.worldedit.math.BlockVector3;
import lombok.experimental.UtilityClass;
import org.bson.Document;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;

@UtilityClass
public class LocationUtils {

    public boolean isSame(Location location, Location otherLocation) {
        return
                location.getWorld().getName().equals(otherLocation.getWorld().getName()) &&
                location.getBlockX() == otherLocation.getBlockX() &&
                location.getBlockY() == otherLocation.getBlockY() &&
                location.getBlockZ() == otherLocation.getBlockZ();
    }

    public Document serializeLocation(Location location) {
        Document document = new Document();
        document.append("world", location.getWorld() == null ? "world" : location.getWorld().getName());
        document.append("x", location.getX());
        document.append("y", location.getY());
        document.append("z", location.getZ());
        document.append("yaw", location.getYaw());
        document.append("pitch", location.getPitch());
        return document;
    }

    public Location deserializeLocation(Document document) {
        return new Location(
                Bukkit.getWorld(document.getString("world")),
                document.get("x", Number.class).doubleValue(),
                document.get("y", Number.class).doubleValue(),
                document.get("z", Number.class).doubleValue(),
                document.get("yaw", Number.class).floatValue(),
                document.get("pitch", Number.class).floatValue()
        );
    }

    public String serializeLocationString(Location location) {
        return location.getWorld().getName() + "," + location.getX() + "," + location.getY() + "," + location.getZ() + "," + location.getYaw() + "," + location.getPitch();
    }

    public Location deserializeLocationString(String location) {
        String[] split = location.split(",");
        return new Location(
                Bukkit.getWorld(split[0]),
                Double.parseDouble(split[1]),
                Double.parseDouble(split[2]),
                Double.parseDouble(split[3]),
                Float.parseFloat(split[4]),
                Float.parseFloat(split[5])
        );
    }

    public static String legacySerializeLocation(Location location) {

        if (location == null) return "";

        return location.getWorld().getName() + ":" + location.getX() + ":" + location.getY() + ":" + location.getZ() + ":" + location.getYaw() + ":" + location.getPitch();
    }

    public static Location legacyDeserialzeLocation(String string) {

        if (string == null || !string.contains(":")) return null;

        String[] build = string.split(":");
        World world = Bukkit.getWorld(build[0]);
        double x = Double.parseDouble(build[1]);
        double y = Double.parseDouble(build[2]);
        double z = Double.parseDouble(build[3]);
        float yaw = Float.parseFloat(build[4]);
        float pitch = Float.parseFloat(build[5]);
        return new Location(world, x, y, z, yaw, pitch);
    }

    public static String toPrettyString(Location location) {
        return location.getBlockX() + " " + location.getBlockY() + " " + location.getBlockZ();
    }

    public static List<Block> getNearbyBlocksByType(Location center, int radius, Material blockType) {
        List<Block> blocks = new ArrayList<>();
        World world = center.getWorld();

        for (int x = center.getBlockX() - radius; x <= center.getBlockX() + radius; x++) {
            for (int y = center.getBlockY() - radius; y <= center.getBlockY() + radius; y++) {
                if (y > 300 || y < 0) continue;
                for (int z = center.getBlockZ() - radius; z <= center.getBlockZ() + radius; z++) {
                    Block block = world.getBlockAt(x, y, z);

                    if (block.getType() == blockType) {
                        blocks.add(block);
                    }
                }
            }
        }

        return blocks;
    }

    public static void runInNearbyBlocksByType(Location center, int radius, Material blockType, Consumer<Block> consumer) {
        World world = center.getWorld();
        int centerX = center.getBlockX();
        int centerY = center.getBlockY();
        int centerZ = center.getBlockZ();
        int radiusSquared = radius * radius;

        int minY = Math.max(world.getMinHeight(), centerY - radius);
        int maxY = Math.min(world.getMaxHeight(), centerY + radius);

        for (int x = -radius; x <= radius; x++) {
            int xSquared = x * x;
            for (int z = -radius; z <= radius; z++) {
                int zSquared = z * z;

                if (xSquared + zSquared > radiusSquared) {
                    continue;
                }

                for (int y = minY; y <= maxY; y++) {
                    int ySquared = (y - centerY) * (y - centerY);

                    if (xSquared + ySquared + zSquared <= radiusSquared) {
                        Block block = world.getBlockAt(centerX + x, y, centerZ + z);
                        if (block.getType() == blockType) {
                            consumer.accept(block);
                        }
                    }
                }
            }
        }
    }

    public static String toString(BlockVector3 vector) {
        return vector.getX() + "," + vector.getY() + "," + vector.getZ();
    }

    public static BlockVector3 fromString(String string) {
        String[] parts = string.split(",");
        if (parts.length != 3) {
            throw new IllegalArgumentException("Invalid BlockVector3 string: " + string);
        }
        return BlockVector3.at(
                Integer.parseInt(parts[0]),
                Integer.parseInt(parts[1]),
                Integer.parseInt(parts[2])
        );
    }

    public static boolean isLocationInArea(Location loc, Location corner1, Location corner2) {
        double minX = Math.min(corner1.getX(), corner2.getX());
        double maxX = Math.max(corner1.getX(), corner2.getX());

        double minY = Math.min(corner1.getY(), corner2.getY());
        double maxY = Math.max(corner1.getY(), corner2.getY());

        double minZ = Math.min(corner1.getZ(), corner2.getZ());
        double maxZ = Math.max(corner1.getZ(), corner2.getZ());

        return loc.getX() >= minX && loc.getX() <= maxX
                && loc.getY() >= minY && loc.getY() <= maxY
                && loc.getZ() >= minZ && loc.getZ() <= maxZ;
    }
}
