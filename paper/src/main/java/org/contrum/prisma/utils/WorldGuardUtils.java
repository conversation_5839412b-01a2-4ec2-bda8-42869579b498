package org.contrum.prisma.utils;

import com.sk89q.worldedit.IncompleteRegionException;
import com.sk89q.worldedit.LocalSession;
import com.sk89q.worldedit.WorldEdit;
import com.sk89q.worldedit.bukkit.BukkitAdapter;
import com.sk89q.worldedit.bukkit.BukkitWorld;
import com.sk89q.worldedit.regions.CuboidRegion;
import com.sk89q.worldedit.regions.Region;
import com.sk89q.worldedit.session.SessionManager;
import com.sk89q.worldguard.WorldGuard;
import com.sk89q.worldguard.protection.ApplicableRegionSet;
import com.sk89q.worldguard.protection.association.RegionAssociable;
import com.sk89q.worldguard.protection.flags.StateFlag;
import com.sk89q.worldguard.protection.managers.RegionManager;
import com.sk89q.worldguard.protection.regions.ProtectedRegion;
import com.sk89q.worldguard.protection.regions.RegionContainer;
import com.sk89q.worldguard.protection.regions.RegionQuery;
import lombok.SneakyThrows;
import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;

public final class WorldGuardUtils {
    private static final RegionContainer container = WorldGuard.getInstance().getPlatform().getRegionContainer();

    private WorldGuardUtils() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    public static boolean isDeny(Location location, StateFlag... flags) {
        RegionManager regions = container.get(BukkitAdapter.adapt(location.getWorld()));
        if (regions == null) {
            return false;
        }
        RegionQuery query = container.createQuery();
        ApplicableRegionSet set = query.getApplicableRegions(BukkitAdapter.adapt(location));
        return set.getRegions().size() != 0 && !set.testState(null, flags);
    }

    public static boolean isAllow(Location location, StateFlag... flags) {
        RegionManager regions = container.get(BukkitAdapter.adapt(location.getWorld()));
        if (regions == null) {
            return false;
        }
        RegionQuery query = container.createQuery();
        ApplicableRegionSet set = query.getApplicableRegions(BukkitAdapter.adapt(location));
        if (set.getRegions().isEmpty()) {
            return false;
        }
        return set.testState(null, flags);
    }

    public static boolean isOnRegion(Location location, String regionName) {
        RegionManager regions = container.get(BukkitAdapter.adapt(location.getWorld()));
        if (regions == null) {
            return false;
        }

        RegionQuery query = container.createQuery();
        ApplicableRegionSet set = query.getApplicableRegions(BukkitAdapter.adapt(location));

        if (set.getRegions().isEmpty()) {
            return false;
        }

        return set.getRegions().stream().anyMatch(region -> region.getId().equalsIgnoreCase(regionName));
    }

    public static boolean isOnlyGlobalRegion(Location location) {
        RegionManager regions = container.get(BukkitAdapter.adapt(location.getWorld()));
        if (regions == null) {
            return false;
        }
        RegionQuery query = container.createQuery();
        ApplicableRegionSet set = query.getApplicableRegions(BukkitAdapter.adapt(location));
        if (set.getRegions().size() == 0) {
            return false;
        }

        return set.getRegions().stream().anyMatch(region -> region.getId().equalsIgnoreCase("global")) && set.getRegions().size() == 1;
    }

    public static ApplicableRegionSet getRegionsAt(Location location) {
        RegionManager regions = container.get(BukkitAdapter.adapt(location.getWorld()));
        if (regions == null) {
            return null;
        }
        RegionQuery query = container.createQuery();

        return query.getApplicableRegions(BukkitAdapter.adapt(location));
    }

    public static boolean containNameRegion(Location location, String regionName) {
        RegionManager regions = container.get(BukkitAdapter.adapt(location.getWorld()));
        if (regions == null) {
            return false;
        }
        RegionQuery query = container.createQuery();
        ApplicableRegionSet set = query.getApplicableRegions(BukkitAdapter.adapt(location));
        if (set.getRegions().size() == 0) {
            return false;
        }
        return set.getRegions().stream().anyMatch(region -> region.getId().contains(regionName));
    }

    public static Location getPos1(Player player){
        // get a LocalSession as per the above example
        SessionManager manager = WorldEdit.getInstance().getSessionManager();

        LocalSession localSession = manager.findByName(player.getName());
        if (localSession == null) return null;

        Region selectionWorld;
        try {
            selectionWorld = localSession.getSelection();
        } catch (IncompleteRegionException e) {
            return null;
        }
        if (selectionWorld == null) return null;

        return new Location(player.getWorld(), selectionWorld.getMinimumPoint().getBlockX(), selectionWorld.getMinimumPoint().getBlockY(), selectionWorld.getMinimumPoint().getBlockZ());
    }

    public static Location getPos2(Player player){
        // get a LocalSession as per the above example
        SessionManager manager = WorldEdit.getInstance().getSessionManager();

        LocalSession localSession = manager.findByName(player.getName());
        if (localSession == null) return null;
        Region selectionWorld;
        try {
            selectionWorld = localSession.getSelection();
        } catch (IncompleteRegionException e) {
            return null;
        }
        if (selectionWorld == null) return null;

        return new Location(player.getWorld(), selectionWorld.getMaximumPoint().getBlockX(), selectionWorld.getMaximumPoint().getBlockY(), selectionWorld.getMaximumPoint().getBlockZ());
    }

    public static CuboidRegion getSelectedRegion(Player player){
        // get a LocalSession as per the above example
        SessionManager manager = WorldEdit.getInstance().getSessionManager();

        LocalSession localSession = manager.findByName(player.getName());
        if (localSession == null) return null;
        Region selectionWorld;
        try {
            selectionWorld = localSession.getSelection();
        } catch (IncompleteRegionException e) {
            return null;
        }
        if (selectionWorld == null) return null;

        CuboidRegion boundingBox = selectionWorld.getBoundingBox();
        boundingBox.setWorld(new BukkitWorld(player.getWorld()));
        return boundingBox;
    }

    public static List<String> getRegionsName(Location location) {
        RegionManager regions = container.get(BukkitAdapter.adapt(location.getWorld()));
        if (regions == null) {
            return new ArrayList<>();
        } else {
            RegionQuery query = container.createQuery();
            ApplicableRegionSet set = query.getApplicableRegions(BukkitAdapter.adapt(location));
            return set.getRegions().isEmpty() ? new ArrayList<>() : set.getRegions().stream().map(ProtectedRegion::getId).toList();
        }
    }
}
