package org.contrum.prisma.utils.menus.geyser.wrappers.customform.items.impl;

import lombok.SneakyThrows;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.contrum.prisma.utils.menus.geyser.wrappers.FormWrapper;
import org.contrum.prisma.utils.menus.geyser.wrappers.customform.items.CustomFormResponsibleItem;
import org.contrum.prisma.utils.menus.geyser.wrappers.simpleform.ButtonWrapper;
import org.geysermc.cumulus.component.ButtonComponent;
import org.geysermc.cumulus.component.DropdownComponent;
import org.geysermc.cumulus.component.InputComponent;
import org.geysermc.cumulus.form.CustomForm;
import org.geysermc.cumulus.form.impl.FormImpl;
import org.geysermc.cumulus.response.CustomFormResponse;

import java.lang.reflect.Method;
import java.util.Arrays;

public class InputFormItem extends CustomFormResponsibleItem<InputComponent, String> {

    public InputFormItem(InputComponent input) {
        super(input);
    }

    @Override
    protected String getResult(CustomFormResponse response, int index) {
        return response.asInput(index);
    }

    @Override
    public void apply(CustomForm.Builder builder) {
        builder.component(super.getValue());
    }

    public static InputFormItem of(InputComponent component) {
        return new InputFormItem(component);
    }

    @SneakyThrows
    public static InputFormItem of(FormWrapper wrapper, @NonNull String text, @NonNull String placeholder, @NonNull String defaultText) {
        Method method = FormImpl.Builder.class.getDeclaredMethod("translate", String.class);
        method.setAccessible(true);

        return new InputFormItem(InputComponent.of(
                (String) method.invoke(wrapper.getBuilder(), text),
                (String) method.invoke(wrapper.getBuilder(), placeholder),
                defaultText
        ));
    }

    @SneakyThrows
    public static InputFormItem of(FormWrapper wrapper, @NonNull String text, @NonNull String placeHolder) {
        Method method = FormImpl.Builder.class.getDeclaredMethod("translate", String.class);
        method.setAccessible(true);

        return new InputFormItem(InputComponent.of(
                (String) method.invoke(wrapper.getBuilder(), text),
                placeHolder
        ));
    }

    @SneakyThrows
    public static InputFormItem of(FormWrapper wrapper, @NonNull String text) {
        Method method = FormImpl.Builder.class.getDeclaredMethod("translate", String.class);
        method.setAccessible(true);

        return new InputFormItem(InputComponent.of(
                (String) method.invoke(wrapper.getBuilder(), text)
        ));
    }
}
