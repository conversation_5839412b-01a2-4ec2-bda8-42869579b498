package org.contrum.prisma.utils.commands;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.Dependency;
import co.aikar.commands.annotation.PreCommand;
import org.bukkit.command.CommandSender;
import org.contrum.tritosa.Translator;

public abstract class ToggleableBaseCommand extends BaseCommand {
    @Dependency private Translator translator;

    public abstract boolean isEnabled();

    @PreCommand
    public boolean preCommand(CommandSender sender) {
        if (!this.isEnabled()){
            translator.send(sender, "COMMANDS.DISABLED");
            return true;
        }
        return false;
    }
}
