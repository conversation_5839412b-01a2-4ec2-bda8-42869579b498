package org.contrum.prisma.utils;

import com.drtshock.playervaults.PlayerVaults;
import com.drtshock.playervaults.vaultmanagement.VaultManager;
import com.drtshock.playervaults.vaultmanagement.VaultOperations;
import com.drtshock.playervaults.vaultmanagement.VaultViewInfo;
import lombok.experimental.UtilityClass;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.inventory.Inventory;

import java.util.HashSet;
import java.util.Set;
import java.util.UUID;

@UtilityClass
public class PlayerVaultsUtils {

    public Set<Integer> getVaultsNumber(UUID uuid) {
        Set<Integer> vaults = new HashSet<>();

        YamlConfiguration file = VaultManager.getInstance().getPlayerVaultFile(uuid.toString(), false);
        if (file == null) {
            return vaults;
        }

        for (String key : file.getKeys(false)) {
            String str = key.replace("vault", "").replace(" ", "");
            vaults.add(Integer.valueOf(str));
        }
        return vaults;
    }

    public int getOpenVault(UUID uuid) {
        VaultViewInfo vaultInfo = PlayerVaults.getInstance().getInVault().get(uuid.toString());
        return vaultInfo == null ? -1 : vaultInfo.getNumber();
    }

    public Inventory loadVault(UUID owner, int vaultNumber) {
        int maxVaultSize = VaultOperations.getMaxVaultSize(owner.toString());
        return VaultManager.getInstance().loadOtherVault(owner.toString(), vaultNumber, maxVaultSize);
    }

    public void saveVault(UUID uuid, Inventory vault, int vaultNumber) {
        VaultManager.getInstance().saveVault(vault, uuid.toString(), vaultNumber);
    }
}
