package org.contrum.prisma.utils.menus.geyser.wrappers;

import lombok.Getter;
import org.geysermc.cumulus.form.Form;
import org.geysermc.cumulus.form.util.FormBuilder;
import org.geysermc.cumulus.response.FormResponse;

@Getter
public abstract class FormWrapper<T extends FormWrapper<T, F, B, R>, F extends Form, B extends FormBuilder<B, F, R>, R extends FormResponse> {

    private final B builder;

    public FormWrapper(B builder) {
        this.builder = builder;
    }

    public void tittle(String title) {
        builder.title(title);
    }

    public F getForm() {
        return builder
                .validResultHandler(this::handleValidResult)
                .build();
    }

    public abstract void handleValidResult(R response);
}
