package org.contrum.prisma.utils.config.reward.item;

import org.bukkit.configuration.ConfigurationSection;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmor;

public class CompressConfigItem extends ConfigItem {

    private CustomArmor armor;

    public CompressConfigItem(PaperServices services, ConfigurationSection section) {
        super();

        CustomArmor customArmor = services.getCustomArmorService().getArmor(section.getString("COMPRESS", "empty"));
        if (customArmor == null) {
            services.getLogger().warning("CompressConfigItem: Custom armor not found for " + section.getString("COMPRESS"));
            return;
        }

        this.armor = customArmor;
        super.setItem(services.getCustomArmorService().getArmorCompress(customArmor, 1));
    }

    @Override
    public void serialize(ConfigurationSection section) {
        if (armor != null) {
            section.set("COMPRESS", armor.getName());
        }
    }
}
