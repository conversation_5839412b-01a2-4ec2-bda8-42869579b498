package org.contrum.prisma.prompts.menu;

import lombok.RequiredArgsConstructor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.PaginatedMenu;
import org.contrum.chorpu.menu.storage.StorageMenu;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.ServerContext;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;

@RequiredArgsConstructor
public class PromptSelectContextMenu extends PaginatedMenu {

    private final PaperServices services;
    private final Consumer<ServerContext> consumer;

    @Override
    public List<Button> getPaginatedButtons(Player player) {
        List<Button> buttons = new ArrayList<>();

        ItemBuilder1_20 globalItem = new ItemBuilder1_20(Material.GREEN_WOOL);
        globalItem.name("&eGlobal");
        buttons.add(Button.of(globalItem.build(), (c) -> {
            consumer.accept(ServerContext.GLOBAL);
        }));

        Set<PrismaServer> servers = services.getServersService().getServers();

        for (PrismaServer server : servers) {
            if (server.getType().isGameMode()) {
                ItemBuilder1_20 item = new ItemBuilder1_20(Material.LIME_WOOL);
                item.name("&e" + server.getName());
                buttons.add(Button.of(item.build(), (c) -> {
                    consumer.accept(ServerContext.build(server));
                }));
            }
        }

        return buttons;
    }

    @Override
    public Map<Integer, Button> getGlobalButtons(Player player) {
        return Map.of();
    }

    @Override
    public StorageMenu.FillType getFillType() {
        return StorageMenu.FillType.ONLY_CORNERS;
    }

    @Override
    public int getRows(Player player) {
        return 4;
    }

    @Override
    public String getTitle(Player player) {
        return "Select server";
    }
}
