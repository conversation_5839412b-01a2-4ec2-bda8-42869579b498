package org.contrum.prisma.disguise;

import com.destroystokyo.paper.profile.PlayerProfile;
import com.github.retrooper.packetevents.PacketEvents;
import com.github.retrooper.packetevents.PacketEventsAPI;
import com.github.retrooper.packetevents.protocol.player.*;
import com.github.retrooper.packetevents.wrapper.PacketWrapper;
import com.github.retrooper.packetevents.wrapper.play.server.*;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import io.github.retrooper.packetevents.util.SpigotConversionUtil;
import io.papermc.paper.event.packet.PlayerChunkLoadEvent;
import io.papermc.paper.event.player.PlayerTrackEntityEvent;
import lombok.Getter;
import me.neznamy.tab.api.TabAPI;
import me.neznamy.tab.api.TabPlayer;
import me.neznamy.tab.api.event.player.PlayerLoadEvent;
import net.kyori.adventure.text.Component;
import org.bukkit.Bukkit;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.profile.PlayerTextures;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.events.PlayerDisguiseEvent;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.rank.Rank;
import org.contrum.prisma.utils.WorldGuardUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.config.ConfigFile;
import org.contrum.prisma.utils.time.TimeUtils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

@Getter
public class DisguiseService implements Listener {
    private final PaperServices services;

    private final ConfigFile config;

    private final List<String> nicknames = new ArrayList<>();
    private final Set<String> ranks = Set.of("Amethyst", "Prisma", "default", "Amber", "Sapphire", "Ruby");

    private final Map<String, Long> blockedNicks = new HashMap<>();

    public DisguiseService(PaperServices services) {
        this.services = services;

        this.config = new ConfigFile(services.getPlugin(), "data/disguise.yml");

        Bukkit.getLogger().info("Loading disguise names...");
        this.searchFile(nicknames, "data/disguise_names.txt");
        Bukkit.getLogger().info("Loaded: " + ranks.size() + " names!");

        //Load blocked nicks
        config.getConfig().getMapList("DATA.BLOCKED_NICKS").forEach(map -> {
            String nick = (String) map.get("nick");
            long time = (long) map.get("time");
            blockedNicks.put(nick, time);
        });

        Bukkit.getPluginManager().registerEvents(this, services.getPlugin());
    }

    public void shutdown() {
        config.getConfig().set("DATA.BLOCKED_NICKS", blockedNicks);
        config.save();
    }

    public Rank getRandomRank() {
        List<String> rankList = this.ranks.stream().toList();
        String rankName = rankList.get(ThreadLocalRandom.current().nextInt(rankList.size()));
        return services.getRankService().getRank(rankName);
    }

    public void disguise(Player player, String name, Rank rank) {
        Profile profile = services.getProfileService().getProfile(player.getUniqueId());
        ProfilePaperMetadata metadata = profile.getServerMetadata(services, ProfilePaperMetadata.class);

        if (metadata.hasCooldown("disguise") && !player.hasPermission("core.disguise.cooldown")) {
            services.getTranslator().send(player, "DISGUISE.ON_COOLDOWN", metadata.getCooldown("disguise").getTimeLeftAsDuration());
            return;
        }

        for (Profile disguisedPlayer : this.getDisguisedPlayers()) {
            if (disguisedPlayer.getDisguiseNick().equalsIgnoreCase(name)) {
                services.getTranslator().send(player, "DISGUISE.DISGUISE_NICK_ALREADY_USED", disguisedPlayer.getName());
                this.unDisguise(player);
                return;
            }
        }

        //Call event
        PlayerDisguiseEvent event = new PlayerDisguiseEvent(player, profile, name, rank, true);
        if (!event.callEvent()) return;

        metadata.disguise(name, rank);
        services.getTranslator().send(player, "DISGUISE.ENABLED", profile);

        this.updatePlayer(player);
        WorldUtils.playSound(Sound.ENTITY_ARROW_HIT_PLAYER, player);

        //Apply cooldown
        if (!player.hasPermission("core.disguise.cooldown"))
            metadata.setCooldown("disguise", TimeUtils.parseDuration(PrismaCoreSetting.DISGUISE_COOLDOWN));
        this.blockNick(name);
    }

    public void unDisguise(Player player) {
        Profile profile = services.getProfileService().getProfile(player.getUniqueId());
        ProfilePaperMetadata metadata = profile.getServerMetadata(services, ProfilePaperMetadata.class);

        if (!profile.isDisguised()) {
            services.getTranslator().send(player, "DISGUISE.NOT_DISGUISED", profile);
            return;
        }

        PlayerDisguiseEvent event = new PlayerDisguiseEvent(player, profile, profile.getRealName(), profile.getActiveGrant().getRank(), false);
        if (!event.callEvent()) return;

        metadata.unDisguise();
        this.updatePlayer(player);

        services.getTranslator().send(player, "DISGUISE.DISABLED", profile);
        WorldUtils.playSound(Sound.ENTITY_HORSE_SADDLE, player, 1000, 0);
    }

    private void updatePlayer(Player player) {
        Profile profile = services.getProfileService().getProfile(player.getUniqueId());
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        String name = profile.getName();

        //Update tab
        TabPlayer tabPlayer = TabAPI.getInstance().getPlayer(player.getUniqueId());
        if (metadata.isDisguised()) {
            tabPlayer.setTemporaryGroup(metadata.getDisguiseRank().getName());
        } else {
            tabPlayer.setTemporaryGroup(null);
        }

        //Update skin & profile nick
        TaskUtil.runAsync(services.getPlugin(), () -> {
            PlayerProfile prof = metadata.isDisguised()
                        ? (PlayerProfile) player.getPlayerProfile().clone()
                        : Bukkit.createProfileExact(player.getUniqueId(), name);


            //Load skin and set fake name if disguised
            if (metadata.isDisguised()) {

                //Cache skin before overwriting it :D
                if (metadata.getCachedPlayerTexture() == null) {
                    PlayerProfile cloneProfile = (PlayerProfile) prof.clone();

                    metadata.setCachedPlayerTexture(cloneProfile.getTextures());
                }

                //Update profile name and skin
                prof.setName(name);
                try {
                    UUID uuid = this.getUUIDFromName(name);
                    if (uuid != null) {
                        String[] skinData = this.getSkinFromUUID(uuid);
                        PlayerTextures textures = prof.getTextures();
                        textures.setSkin(new URL(skinData[0]), PlayerTextures.SkinModel.valueOf(skinData[1].toUpperCase()));
                        textures.toString(); // We call toString to make sure the textures are loaded (load method is inaccessible)
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else {
                //Load cached skin if exist
                Object cachedTexture = metadata.getCachedPlayerTexture();
                if (cachedTexture instanceof PlayerTextures textures) {
                    textures.toString(); // We call toString to make sure the textures are loaded (load method is inaccessible)
                    prof.setTextures(textures);
                    metadata.setCachedPlayerTexture(null);
                }
            }

            //Apply new profile & send skin packets sync
            TaskUtil.run(services.getPlugin(), () -> {
                player.setPlayerProfile(prof);
                if (metadata.isDisguised())
                    this.updatePlayerSkinFor(player, Bukkit.getOnlinePlayers());
            });
        });
    }

    private void updatePlayerSkinFor(Player player, Collection<? extends Player> viewers) {
        Profile coreProfile = services.getProfileService().getProfile(player.getUniqueId());
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);

        PacketEventsAPI<?> packetEvents = PacketEvents.getAPI();
        if (!metadata.isDisguised()) return;

        //Get skin data
        TaskUtil.runAsync(services.getPlugin(), () -> {
            String name = coreProfile.getName();

            String textureValue = null;
            String textureSignature = null;

            UUID uuid = this.getUUIDFromName(name);

            if (uuid != null) {
                String[] skinData = getSkinSignatureByUUID(uuid);
                if (skinData != null) {
                    textureValue = skinData[0];
                    textureSignature = skinData[1];
                }
            }

            User user = packetEvents.getPlayerManager().getUser(player);
            UserProfile profile = user.getProfile();

            GameMode gamemode = player.getGameMode();
            int ping = packetEvents.getPlayerManager().getPing(player);

            WrapperPlayServerPlayerInfoRemove remove = new WrapperPlayServerPlayerInfoRemove(profile.getUUID());
            WrapperPlayServerDestroyEntities destroy = new WrapperPlayServerDestroyEntities(user.getEntityId());

            profile.getTextureProperties().removeIf(property -> property.getName().equalsIgnoreCase("textures"));
            if (textureValue != null && textureSignature != null)
                profile.getTextureProperties().add(new TextureProperty("textures", textureValue, textureSignature));
            profile.setName(name);

            WrapperPlayServerPlayerInfoUpdate add = new WrapperPlayServerPlayerInfoUpdate(
                    WrapperPlayServerPlayerInfoUpdate.Action.ADD_PLAYER,
                    new WrapperPlayServerPlayerInfoUpdate.PlayerInfo(
                            profile,
                            true,
                            ping,
                            SpigotConversionUtil.fromBukkitGameMode(gamemode),
                            Component.text(name),
                            null
                    )
            );

            WrapperPlayServerPlayerInfoUpdate update = new WrapperPlayServerPlayerInfoUpdate(
                    WrapperPlayServerPlayerInfoUpdate.Action.UPDATE_LISTED,
                    new WrapperPlayServerPlayerInfoUpdate.PlayerInfo(
                            profile,
                            true,
                            ping,
                            SpigotConversionUtil.fromBukkitGameMode(gamemode),
                            Component.text(name),
                            null
                    )
            );

            WrapperPlayServerEntityEquipment equipment = new WrapperPlayServerEntityEquipment(
                    player.getEntityId(),
                    List.of(
                            new Equipment(EquipmentSlot.HELMET, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getHelmet())),
                            new Equipment(EquipmentSlot.CHEST_PLATE, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getChestplate())),
                            new Equipment(EquipmentSlot.LEGGINGS, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getLeggings())),
                            new Equipment(EquipmentSlot.BOOTS, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getBoots())),
                            new Equipment(EquipmentSlot.OFF_HAND, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getItemInOffHand())),
                            new Equipment(EquipmentSlot.MAIN_HAND, SpigotConversionUtil.fromBukkitItemStack(player.getInventory().getItemInMainHand()))
                    )
            );
            WrapperPlayServerSpawnPlayer spawn = new WrapperPlayServerSpawnPlayer(user.getEntityId(), profile.getUUID(), SpigotConversionUtil.fromBukkitLocation(player.getLocation()));

            List<PacketWrapper<?>> packets = new ArrayList<>();
            if (player.isUntracked()) {
                packets.addAll(
                        List.of(remove, add, update)
                );
            } else {
                packets.addAll(
                        List.of(remove, destroy, add, spawn, update, equipment)
                );
            }

            for (Player viewer : viewers) {
                if (viewer.equals(player)) return;

                packets.forEach(wrapper -> packetEvents.getPlayerManager().sendPacket(viewer, wrapper));
            }
        });
    }

    public boolean isDisguised(UUID uuid) {
        for (Profile p : this.getDisguisedPlayers()) {
            if (p.getUniqueId().equals(uuid)) return true;
        }

        return false;
    }

    public boolean isDisguise(String nick) {
        for (Profile p : this.getDisguisedPlayers()) {
            if (p.getName().equals(nick)) return true;
        }

        return false;
    }

    public List<Profile> getDisguisedPlayers() {
        return services.getProfileService().getOnlineProfiles().values().stream().filter(Profile::isDisguised).toList();
    }

    public List<String> getAvailableNicknames() {
        List<String> names = new ArrayList<>(this.nicknames);

        Set<String> expiredNicks = new HashSet<>();
        for (Map.Entry<String, Long> entry : blockedNicks.entrySet()) {
            if (entry.getValue() > System.currentTimeMillis()) {
                names.remove(entry.getKey());
                continue;
            }

            expiredNicks.add(entry.getKey());
        }

        expiredNicks.forEach(blockedNicks::remove);
        return names;
    }

    public void blockNick(String nick) {
        blockedNicks.put(nick, System.currentTimeMillis() + TimeUtils.parseDuration(PrismaCoreSetting.DISGUISE_NICK_COOLDOWN).toMillis());
    }

    @EventHandler
    public void onJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);

        //Update disguise
        if (metadata.isDisguised()) {

            // Check if disguise nick is already in use
            for (Profile disguisedPlayer : this.getDisguisedPlayers()) {
                if (!disguisedPlayer.equals(metadata.getProfile()) && disguisedPlayer.getDisguiseNick().equalsIgnoreCase(metadata.getDisguiseNick())) {
                    services.getTranslator().send(player, "DISGUISE.DISGUISE_NICK_ALREADY_USED", disguisedPlayer.getName());
                    metadata.setDisguised(false);
                    this.unDisguise(player);
                    return;
                }
            }

            metadata.disguise(metadata.getDisguiseNick(), metadata.getDisguiseRank());
        } else {
            metadata.unDisguise();
        }

        TaskUtil.runLater(services.getPlugin(), () -> {
            if (metadata.isDisguised()) {
                this.updatePlayer(player);
            }

            if (!player.isUntracked()) {
                Bukkit.getOnlinePlayers().forEach(p -> {
                    if (p.equals(player)) return;

                    ProfilePaperMetadata mt = services.getProfileService().getServerMetadata(p.getUniqueId(), ProfilePaperMetadata.class);
                    if (mt.isDisguised() && !p.isUntracked())
                        this.updatePlayerSkinFor(p, List.of(player));
                });
            }
        }, 20L);
    }

    @EventHandler
    public void loadPlayer(PlayerTrackEntityEvent event) {
        Player player = event.getPlayer();

        if (true) return;

        if (event.getEntity() instanceof Player target && this.isDisguised(target.getUniqueId()) && !player.isUntracked() && !target.isUntracked()) {
            TaskUtil.runLater(services.getPlugin(), () -> {
                WrapperPlayServerSpawnPlayer spawn = new WrapperPlayServerSpawnPlayer(target.getEntityId(), target.getUniqueId(), SpigotConversionUtil.fromBukkitLocation(target.getLocation()));

                WrapperPlayServerEntityEquipment equipment = new WrapperPlayServerEntityEquipment(
                        target.getEntityId(),
                        List.of(
                                new Equipment(EquipmentSlot.HELMET, SpigotConversionUtil.fromBukkitItemStack(target.getInventory().getHelmet())),
                                new Equipment(EquipmentSlot.CHEST_PLATE, SpigotConversionUtil.fromBukkitItemStack(target.getInventory().getChestplate())),
                                new Equipment(EquipmentSlot.LEGGINGS, SpigotConversionUtil.fromBukkitItemStack(target.getInventory().getLeggings())),
                                new Equipment(EquipmentSlot.BOOTS, SpigotConversionUtil.fromBukkitItemStack(target.getInventory().getBoots())),
                                new Equipment(EquipmentSlot.OFF_HAND, SpigotConversionUtil.fromBukkitItemStack(target.getInventory().getItemInOffHand())),
                                new Equipment(EquipmentSlot.MAIN_HAND, SpigotConversionUtil.fromBukkitItemStack(target.getInventory().getItemInMainHand()))
                        )
                );

                PacketEvents.getAPI().getPlayerManager().sendPacket(player, spawn);
                PacketEvents.getAPI().getPlayerManager().sendPacket(player, equipment);
            }, 20L);
        }
    }

    private UUID getUUIDFromName(String name) {
        try {
            String url = "https://api.mojang.com/users/profiles/minecraft/" + name;
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setReadTimeout(5000);
            connection.setConnectTimeout(5000);
            connection.setRequestMethod("GET");

            InputStreamReader reader = new InputStreamReader(connection.getInputStream());
            JsonObject json = JsonParser.parseReader(reader).getAsJsonObject();
            String uuidString = json.get("id").getAsString();
            return UUID.fromString(uuidString.replaceFirst(
                    "(\\w{8})(\\w{4})(\\w{4})(\\w{4})(\\w{12})", "$1-$2-$3-$4-$5"));
        } catch (Exception e) {
            return null;
        }
    }

    private String[] getSkinSignatureByUUID(UUID uuid) {
        try {
            String url = "https://sessionserver.mojang.com/session/minecraft/profile/" + uuid + "?unsigned=false";
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setReadTimeout(5000);
            connection.setConnectTimeout(5000);
            connection.setRequestMethod("GET");

            InputStreamReader reader = new InputStreamReader(connection.getInputStream());
            JsonObject json = JsonParser.parseReader(reader).getAsJsonObject();
            JsonArray properties = json.getAsJsonArray("properties");

            for (int i = 0; i < properties.size(); i++) {
                JsonObject property = properties.get(i).getAsJsonObject();
                if (property.get("name").getAsString().equals("textures")) {
                    String textureValue = property.get("value").getAsString();
                    String textureSignature = property.get("signature").getAsString();
                    return new String[]{textureValue, textureSignature};
                }
            }
            return null;
        } catch (Exception e) {
            return null;
        }
    }

    private String[] getSkinFromUUID(UUID uuid) {
        try {
            String url = "https://sessionserver.mojang.com/session/minecraft/profile/" + uuid + "?unsigned=false";
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setReadTimeout(5000);
            connection.setConnectTimeout(5000);
            connection.setRequestMethod("GET");

            InputStreamReader reader = new InputStreamReader(connection.getInputStream());
            JsonObject json = JsonParser.parseReader(reader).getAsJsonObject();
            JsonArray properties = json.getAsJsonArray("properties");

            for (int i = 0; i < properties.size(); i++) {
                JsonObject property = properties.get(i).getAsJsonObject();
                if (property.get("name").getAsString().equals("textures")) {
                    String textureValue = property.get("value").getAsString();
                    String decodedValue = new String(Base64.getDecoder().decode(textureValue));

                    JsonObject textureJson = JsonParser.parseString(decodedValue).getAsJsonObject();
                    JsonObject textures = textureJson.getAsJsonObject("textures");

                    String skinUrl = textures.getAsJsonObject("SKIN").get("url").getAsString();

                    String skinModel = "classic";
                    if (textures.getAsJsonObject("SKIN").has("metadata")) {
                        JsonObject metadata = textures.getAsJsonObject("SKIN").getAsJsonObject("metadata");
                        if (metadata.has("model")) {
                            skinModel = metadata.get("model").getAsString();
                        }
                    }

                    return new String[]{skinUrl, skinModel};
                }
            }
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    private void searchFile(List<String> list, String fileName) {
        Path path = services.getPlugin().getDataFolder().toPath().resolve(fileName);
        if (!Files.exists(path)) {
            try {
                Files.createDirectories(path.getParent());
                InputStream stream = services.getPlugin().getResource(fileName);
                Files.copy(stream, path);
                stream.close();
            } catch (IOException ex) {
                services.getLogger().info("Couldn't create directory or file " + fileName);
            }
        }

        try {
            try (BufferedReader reader = Files.newBufferedReader(path, StandardCharsets.UTF_8)) {
                reader.lines().forEach(list::add);
            }
        } catch (IOException ex) {
            services.getLogger().info("Couldn't read file " + fileName + ": " + ex.getLocalizedMessage());
        }
    }
}
