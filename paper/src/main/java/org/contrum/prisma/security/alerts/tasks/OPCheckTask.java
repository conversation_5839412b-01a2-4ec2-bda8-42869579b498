/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by izLoki on 14/05/2024
 * Website: contrum.org
 */

package org.contrum.prisma.security.alerts.tasks;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.security.SecurityService;
import org.contrum.prisma.security.alerts.AlertsManager;
import org.contrum.prisma.utils.DiscordWebhook;

import java.awt.*;
import java.io.IOException;

public class OPCheckTask implements Runnable {

    private final SecurityService securityService;
    private final AlertsManager alertsManager;

    public OPCheckTask(AlertsManager manager) {
        this.securityService = manager.getSecurityService();
        this.alertsManager = manager;
        Bukkit.getScheduler().runTaskTimerAsynchronously(securityService.getServices().getPlugin(), this, 0L, 40L);
    }

    @Override
    public void run() {
        for (Player player : Bukkit.getOnlinePlayers()){
            if (player.isOp()){
                Profile profile = securityService.getServices().getProfileService().getProfile(player.getUniqueId());
                if (!alertsManager.getConfig().getConfig().getStringList("OP.AllowedOPs").contains(profile.getRealName())){

                    TaskUtil.run(securityService.getServices().getPlugin(), ()-> {
                        player.setOp(false);
                        Bukkit.getServer().dispatchCommand(Bukkit.getServer().getConsoleSender(), "prismacore:ban " + player.getName() + " Network Security");

                        DiscordWebhook webhook = new DiscordWebhook(AlertsManager.OP_REMOVED_WEBHOOK);
                        webhook.setUsername("PrismaMC Logs");
                        webhook.setContent("@everyone");
                        webhook.addEmbed(new DiscordWebhook.EmbedObject()
                                .setTitle("**Not whitelisted player got OP! (Banned)**")
                                .setColor(Color.RED)
                                .addField("Server", securityService.getServices().getServersService().getCurrentServer().getName(), false)
                                .addField("PlayerName", player.getName(), false)
                                .addField("World", player.getWorld().getName(), false)

                        );
                        TaskUtil.runAsync(securityService.getServices().getPlugin(), () -> {
                            try {
                                webhook.execute();
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        });
                    });
                };
            }
        }
    }
}
