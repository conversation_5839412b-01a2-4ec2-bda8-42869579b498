package org.contrum.prisma.commands.admin.specialevents.hideandseek;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Dependency;
import co.aikar.commands.annotation.Subcommand;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.utils.WorldGuardUtils;
import org.contrum.tritosa.Translator;

import java.time.Duration;
import java.util.Set;
import java.util.UUID;

@CommandAlias("hideandseek") @CommandPermission("core.command.specialevent")
public class HideAndSeekEventCommand extends BaseCommand {

    /*
    @Dependency private SpecialEventService service;
    @Dependency private Translator translator;

    public HideAndSeekEvent getEvent(CommandSender sender) {
        SpecialEvent activeEvent = service.getActiveEvent();
        if (!(activeEvent instanceof HideAndSeekEvent hideAndSeekEvent)) {
            translator.send(sender, "EVENT.NO_ACTIVE");
            return null;
        }

        return hideAndSeekEvent;
    }

    public HideAndSeekEvent getEventInstance(CommandSender sender) {
        for (SpecialEvent event : service.getEvents().values()) {
            if (event instanceof HideAndSeekEvent h) return h;
        }

        return null;
    }

    @Subcommand("settings addItemLocation")
    public void addItemLocation(Player player) {
        HideAndSeekEvent event = this.getEventInstance(player);
        if (event == null) return;

        event.getItemLocations().add(player.getLocation());
        player.sendMessage(CC.translate("&aLocation set!"));
    }

    @Subcommand("settings clearItemLocations")
    public void clearItemLocations(Player player) {
        HideAndSeekEvent event = this.getEventInstance(player);
        if (event == null) return;

        event.getItemLocations().clear();
        player.sendMessage(CC.translate("&aItem spawn locations have been cleared!"));
    }

    @Subcommand("settings setWaitingLocation")
    public void setWaitingLocation(Player player) {
        HideAndSeekEvent event = this.getEventInstance(player);
        if (event == null) return;

        event.setWaitingLocation(player.getLocation());
        player.sendMessage(CC.translate("&aWaiting location set!"));
    }

    @Subcommand("settings setZone")
    public void setZone(Player player) {
        HideAndSeekEvent event = this.getEventInstance(player);
        if (event == null) return;

        Location pos1 = WorldGuardUtils.getPos1(player);
        Location pos2 = WorldGuardUtils.getPos2(player);
        if (pos1 == null || pos2 == null) {
            player.sendMessage(CC.translate("&cSelecciona una zona con worldedit primero!"));
            return;
        }

        event.setPos1(pos1);
        event.setPos2(pos2);
        player.sendMessage(CC.translate("&aEvent pos1 & pos2 locations set!"));
    }

    @Subcommand("event setWaitingTime")
    public void setWaitingTime(CommandSender sender, Duration duration) {
        HideAndSeekEvent event = this.getEvent(sender);
        if (event == null) return;

        SEventPhase currentPhase = event.getCurrentPhase();
        if (!(currentPhase instanceof HSWaitingPhase phase)) {
            sender.sendMessage(CC.translate("&cEl evento no está en la fase de espera!"));
            return;
        }

        phase.changeTime(duration);
        sender.sendMessage(CC.translate("&aTiempo actualizado!"));
    }

    @Subcommand("event forcestart")
    public void forceStart(CommandSender sender) {
        HideAndSeekEvent event = this.getEvent(sender);
        if (event == null) return;

        SEventPhase currentPhase = event.getCurrentPhase();
        if (!(currentPhase instanceof HSWaitingPhase phase)) {
            sender.sendMessage(CC.translate("&cEl evento no está en la fase de espera!"));
            return;
        }

        phase.stop();
    }

    @Subcommand("event forcejoin")
    public void forceJoin(CommandSender sender, OnlinePlayer player) {
        HideAndSeekEvent event = this.getEvent(sender);
        if (event == null) return;

        event.forceJoin(player.getPlayer());
        sender.sendMessage(CC.translate("&a" + player.getPlayer().getName() + " ha sido forcejoineado!"));
    }

    @Subcommand("event setSeeker")
    public void setSeeker(CommandSender sender, OnlinePlayer player) {
        HideAndSeekEvent event = this.getEvent(sender);
        if (event == null) return;

        event.setSeeker(sender, player.getPlayer());
    }

    @Subcommand("event getSeekers")
    public void getSeekers(CommandSender sender) {
        HideAndSeekEvent event = this.getEvent(sender);
        if (event == null) return;

        Set<UUID> seekers = event.getSeekers();
        sender.sendMessage(CC.translate("&eSeekers: " + seekers.size()));
        for (UUID s : seekers) {
            Player player = Bukkit.getPlayer(s);
            sender.sendMessage(CC.translate("&e- " + player.getName()));
        }
    }

    @Subcommand("event getAlivePlayers")
    public void getALivePlayers(CommandSender sender) {
        HideAndSeekEvent event = this.getEvent(sender);
        if (event == null) return;

        Set<UUID> seekers = event.getPlayersInEvent();
        sender.sendMessage(CC.translate("&ePlayers: " + seekers.size()));
        for (UUID s : seekers) {
            Player player = Bukkit.getPlayer(s);
            if (player == null) continue;
            sender.sendMessage(CC.translate("&e- " + player.getName()));
        }
    }

    @Subcommand("event info")
    public void info(CommandSender sender) {
        HideAndSeekEvent event = this.getEvent(sender);
        if (event == null) return;

        sender.sendMessage(CC.translate("&ePlayers: " + event.getPlayersInEvent().size()));
        sender.sendMessage(CC.translate("&eSeekers: " + event.getSeekers().size()));
        sender.sendMessage(CC.translate("&eSpectators: " + event.getSpectators().size()));
    }

    @Subcommand("event glowing")
    public void glowing(CommandSender sender, Duration duration) {
        HideAndSeekEvent event = this.getEvent(sender);
        if (event == null) return;

        event.giveGlowing(duration);
    }

     */
}
