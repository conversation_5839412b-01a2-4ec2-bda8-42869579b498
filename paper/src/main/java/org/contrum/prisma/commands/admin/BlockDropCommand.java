package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.drops.blockdrops.BlockDropsService;
import org.contrum.prisma.utils.CCP;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@CommandAlias("blockdrop")
@CommandPermission("core.command.blockdrop")
public class BlockDropCommand extends BaseCommand {

    @Dependency private BlockDropsService blockDropsService;

    @Subcommand("add")
    @CommandCompletion("@materials @range:1-100")
    public void add(Player player, String matString, double chance){
        Material material = Material.getMaterial(matString);
        if (material == null || !material.isBlock()){
            player.sendMessage(CC.translate("&cEste material no existe o no es un bloque!"));
            return;
        }


        ItemStack item = player.getInventory().getItemInMainHand();
        if (item.getType().equals(Material.AIR)){
            player.sendMessage(CC.translate("&cTienes que tener un item en la mano!"));
            return;
        }

        blockDropsService.addDrop(material, item.clone(), chance);
        player.sendMessage(CC.translate("&aDrop &e"+item.getType().toString()+" " + chance +  "%  &aagregado!"));
    }

    @Subcommand("list")
    public void list(Player player){
        player.sendMessage(CC.translate("&e&lCustomItems drops:"));
        for (Map.Entry<Material, HashMap<ItemStack, Double>> entry : blockDropsService.getDropMap().entrySet()){
            Material material = entry.getKey();
            HashMap<ItemStack, Double> itemsMap = entry.getValue();
            player.sendMessage(CC.translate("&e- &e" + material + ":"));
            for (Map.Entry<ItemStack, Double> itemEntry : itemsMap.entrySet()){
                player.sendMessage(CC.translate("&f &f &e- &7" + itemEntry.getKey().getType() + " &e" + itemEntry.getValue() + "%"));
            }
        }
    }

    @Subcommand("remove")
    @CommandCompletion("@materials @materials")
    public void remove(Player player, String matString, String matToRemove){
        Material material = Material.matchMaterial(matString);
        if (material == null || !material.isBlock()){
            player.sendMessage(CCP.translateAndFront("&cEste material no existe o no es un bloque!"));
            return;
        }

        HashMap<ItemStack, Double> itemDrops = blockDropsService.getDropMap().get(material);
        if (itemDrops == null){
            player.sendMessage(CCP.translateAndFront("&cEste bloque no tiene custom item drop!"));
            return;
        }

        Material removeMaterial = Material.matchMaterial(matToRemove);
        if (removeMaterial == null){
            player.sendMessage(CCP.translateAndFront("&cEl material &e"+matToRemove+"&c no existe!"));
            return;
        }


        List<ItemStack> toRemove = new ArrayList<>();

        for (Map.Entry<ItemStack, Double> entry : itemDrops.entrySet()) {
            ItemStack i = entry.getKey();
            if (i.getType().equals(removeMaterial)) toRemove.add(i);
        }

        if (toRemove.isEmpty()){
            player.sendMessage(CCP.translateAndFront("&cEl bloque &e"+matString+"&c no tiene el item drop &e" + removeMaterial.name()));
            return;
        }

        //Remove items
        toRemove.forEach(itemDrops::remove);
        blockDropsService.getDropMap().put(material, itemDrops);

        player.sendMessage(CCP.translateAndFront("&aDrop &e"+removeMaterial + " " +  " &aeliminado!"));
    }
}