/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by EnzoLy on 16/04/2024
 * Website: contrum.org
 */

package org.contrum.prisma.commands.user.media.packets;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.contrum.prisma.utils.redis.pyrite.Packet;

@AllArgsConstructor
@Getter
public class StreamPacket extends Packet {

    private String playerName;
    private String link;

}
