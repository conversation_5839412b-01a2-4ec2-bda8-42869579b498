package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import io.papermc.paper.event.player.AsyncChatEvent;
import net.kyori.adventure.text.serializer.legacy.LegacyComponentSerializer;
import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.command.CommandSender;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.tritosa.Translator;

import java.time.Duration;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

@CommandAlias("ggwave")
@CommandPermission("core.command.ggwave")
public class GGWaveCommand extends BaseCommand {

    @Dependency
    private PaperServices services;
    @Dependency
    private Translator translator;

    @Default
    public void start(CommandSender sender, Duration duration) {
        translator.broadcast("GG_WAGE.STARTED", duration);
        WorldUtils.playSound(Sound.ENTITY_PLAYER_LEVELUP);

        Listener temporaryListener = new Listener() {

            private final Set<UUID> claimed = ConcurrentHashMap.newKeySet();

            @EventHandler(priority = EventPriority.LOW)
            public void onPlayerChat(AsyncChatEvent event) {
                UUID uuid = event.getPlayer().getUniqueId();
                if (claimed.contains(uuid)) return;

                String message = LegacyComponentSerializer.legacyAmpersand().serialize(event.message()).toLowerCase();
                if (message.contains("gg")) {
                    claimed.add(uuid);
                    WorldUtils.playSound(Sound.ENTITY_PLAYER_LEVELUP, event.getPlayer());
                    translator.send(event.getPlayer(), "GG_WAGE.WON");

                    TaskUtil.run(services.getPlugin(), () -> {
                        for (String command : PrismaCoreSetting.GG_WAVE_REWARDS) {
                            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command.replaceAll("<player_name>", event.getPlayer().getName()));
                        }
                    });
                }
            }
        };

        Bukkit.getPluginManager().registerEvents(temporaryListener, services.getPlugin());

        TaskUtil.runLater(services.getPlugin(), () -> {
            translator.broadcast("GG_WAGE.ENDED");
            AsyncChatEvent.getHandlerList().unregister(temporaryListener);
        }, 20 * duration.toSeconds());
    }
}
