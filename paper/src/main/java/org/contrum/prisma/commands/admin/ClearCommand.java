package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.contrum.tritosa.Translator;

@CommandAlias("clear|ci") @CommandPermission("core.command.clear")
public class ClearCommand extends BaseCommand {

    @Dependency private Translator translator;

    @Default
    public void clearSelf(Player player) {
        player.getInventory().clear();

        translator.send(player, "COMMANDS.CLEAR.SELF");
    }

    @Default @CommandPermission("core.command.clear.other")
    public void clearOther(CommandSender sender, OnlinePlayer target) {
        Player player = target.getPlayer();
        player.getInventory().clear();

        translator.send(sender, "COMMANDS.CLEAR.OTHER", player);
    }
}