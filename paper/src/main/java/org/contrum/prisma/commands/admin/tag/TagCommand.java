package org.contrum.prisma.commands.admin.tag;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.tag.Tag;
import org.contrum.prisma.tag.TagSection;
import org.contrum.prisma.tag.TagService;
import org.contrum.prisma.tag.menu.SelectTagSectionMenu;
import org.contrum.prisma.tag.menu.SelectTagsMenu;
import org.contrum.prisma.tag.menu.edit.TagEditMenu;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.UUID;

@CommandAlias("tag|tags|prefix")
public class TagCommand extends BaseCommand {

    @Dependency private TagService tagService;
    @Dependency private PaperServices services;

    @Default
    public void tag(Player sender) {
        Profile profile = services.getProfileService().getProfile(sender.getUniqueId());
        ProfilePaperMetadata paperMetadata = profile.getServerMetadata(services, ProfilePaperMetadata.class);

        new SelectTagSectionMenu(services.getPlugin(),
                tagSection -> tagSection.isEnabled() && tagSection.getServerContext().applies(profile.getLastServer()),
                (menu, tagSection) -> {
                    new SelectTagsMenu(services.getPlugin(), tagService,
                            tag -> tag.isEnabled() && tag.getServerContext().applies(profile.getLastServer()) && (tagSection.equals(tag.getSection())),
                            selectedTag -> {
                                if (!sender.hasPermission("core.tags.bypass")) {
                                    if (!profile.canEquipTag(selectedTag.getName())) {
                                        services.getTranslator().send(sender, "TAGS.MENU.TAG.NO_PERMISSION");
                                        return;
                                    }
                                }

                                Tag activeTag = tagService.getTag(profile.getActiveTagName(selectedTag.getServerContext()));

                                if (selectedTag.equals(activeTag)) {
                                    profile.setActiveTag(selectedTag.getServerContext(), null);
                                    services.getTranslator().send(sender, "TAGS.MENU.TAG.UN_SELECTED_TAG",
                                            LocalPlaceholders.builder().add("<tag_name>", selectedTag.getDisplayName()));
                                    sender.playSound(sender.getLocation(), "ui.button.click", 1, 1);
                                } else {
                                    services.getTranslator().send(sender, "TAGS.MENU.TAG.SELECTED_TAG",
                                            LocalPlaceholders.builder().add("<tag_name>", selectedTag.getDisplayName()));
                                    profile.setActiveTag(selectedTag.getServerContext(), selectedTag.getName());
                                }
                            }).setBack(menu::open).open(sender);
                }
        ).open(sender);
    }

    @Subcommand("help")
    @CommandPermission("core.command.tag.manage")
    public void help(CommandSender sender) {
        sender.sendMessage("  ");
        sender.sendMessage(CC.translate("&b&lTags Commands"));
        sender.sendMessage(CC.translate("&7/tag create <name>"));
        sender.sendMessage(CC.translate("&7/tag delete <name>"));
        sender.sendMessage(CC.translate("&7/tag setprefix <name> <prefix>"));
        sender.sendMessage(CC.translate("&7/tag setsuffix <name> <suffix>"));
        sender.sendMessage(CC.translate("&7/tag setpermission <name> <permission>"));
        sender.sendMessage(CC.translate("&7/tag setsection <name> <section>"));
        sender.sendMessage(CC.translate("&7/tag seticon <name> <section>"));
        sender.sendMessage(CC.translate("&7/tag list"));
        sender.sendMessage(CC.translate("&7/tag edit [name]"));
        sender.sendMessage(CC.translate("&7/tag give <name> <player>"));
        sender.sendMessage(CC.translate("&7/tag take <name> <player>"));
        sender.sendMessage(CC.translate("&7/tag all"));
        sender.sendMessage("     ");
    }

    @Subcommand("all")
    @CommandPermission("core.command.tag.manage")
    public void all(Player sender) {
        Profile profile = services.getProfileService().getProfile(sender.getUniqueId());
        ProfilePaperMetadata paperMetadata = profile.getServerMetadata(services, ProfilePaperMetadata.class);

        new SelectTagsMenu(services.getPlugin(), tagService, null, selectedTag -> {
            profile.setActiveTag(selectedTag.getServerContext(), selectedTag.getName());

            services.getTranslator().send(sender,
                    "TAGS.MENU.TAG.SELECTED_TAG",
                    LocalPlaceholders.builder().add("<tag_name>", selectedTag.getName()));
            sender.playSound(sender.getLocation(), "ui.button.click", 1, 1);
        }, true).open(sender);
    }

    @Subcommand("create")
    @Syntax("<name>")
    @CommandPermission("core.command.tag.manage")
    public void create(Player sender, String name) {
        if (tagService.getTag(name) != null) {
            sender.sendMessage(CC.translate("&cTag already exists."));
            return;
        }

        Tag tag = new Tag(name, name);
        tagService.addTag(tag);
        tagService.updateTag(tag);
        sender.sendMessage(CC.translate("&eSuccessfully created tag &f" + name));
    }

    @Subcommand("delete|remove")
    @Syntax("<name>")
    @CommandCompletion("@tags")
    @CommandPermission("core.command.tag.manage")
    public void delete(Player sender, Tag tag) {
        tagService.removeTag(tag);
        sender.sendMessage(CC.translate("&eSuccessfully removed tag &f" + tag.getName()));
        tagService.updateTag(tag, true);
    }

    @Subcommand("setprefix")
    @Syntax("<name> <prefix>")
    @CommandCompletion("@tags")
    @CommandPermission("core.command.tag.manage")
    public void setPrefix(Player sender, Tag tag, String prefix) {
        tag.setPrefix(prefix);
        tagService.updateTag(tag);
        sender.sendMessage(CC.translate("&eSuccessfully set prefix for tag &f" + tag.getName()));
    }

    @Subcommand("setsuffix")
    @Syntax("<name> <suffix>")
    @CommandCompletion("@tags")
    @CommandPermission("core.command.tag.manage")
    public void setSuffix(Player sender, Tag tag, String suffix) {
        tag.setSuffix(suffix);
        tagService.updateTag(tag);
        sender.sendMessage(CC.translate("&eSuccessfully set suffix for tag &f" + tag.getName()));
    }

    @Subcommand("setsection")
    @Syntax("<name> <section>")
    @CommandCompletion("@tags @tagsections")
    @CommandPermission("core.command.tag.manage")
    public void setSection(Player sender, Tag tag, TagSection tagSection) {
        sender.sendMessage(CC.translate("&eSuccessfully added tag &f" + tag.getName() + " &eto section &f" + tagSection.getDisplayName()));
        tag.setSection(tagSection);
        tagService.updateTag(tag);
    }

    @Subcommand("list")
    @CommandPermission("core.command.tag.manage")
    public void tabList(CommandSender sender) {
        sender.sendMessage(CC.translate("&e&lTags:"));
        for (Tag tag : tagService.getTags().values()) {
            sender.sendMessage(CC.translate("&7- &e" + tag.getName() + "["+tag.getPrefix()+"&7] &7- Section: &e" + (tag.getSection() != null ? tag.getSection().getDisplayName() : "&cnone")));
        }
    }

    @Subcommand("edit")
    @Syntax("[name]")
    @CommandCompletion("@tags")
    @CommandPermission("core.command.tag.manage")
    public void edit(Player sender, @Optional Tag tag) {
        if (tag == null) {
            new SelectTagsMenu(services.getPlugin(), tagService, null, selectedTag -> {
                new TagEditMenu(services.getPlugin(), selectedTag).open(sender);
            }, true);
            return;
        }

        new TagEditMenu(services.getPlugin(), tag).open(sender);
    }

    @Subcommand("seticon")
    @Syntax("<name> <section>")
    @CommandCompletion("@tags @tagsections")
    @CommandPermission("core.command.tag.manage")
    public void setIcon(Player sender, Tag tag) {
        ItemStack hand = sender.getInventory().getItemInMainHand();

        if (hand.getType() == org.bukkit.Material.AIR) {
            sender.sendMessage(CC.translate("&cYou must be holding an item."));
            return;
        }

        tag.setIcon(hand);
        tagService.updateTag(tag);
        sender.sendMessage(CC.translate("&eSuccessfully set icon for tag &f" + tag.getName()));
    }

    @Subcommand("give")
    @Syntax("<name> <player>")
    @CommandCompletion("@tags @players")
    @CommandPermission("core.command.tag.manage")
    public void give(CommandSender sender, Tag tag, UUID targetUUID) {
        services.getProfileService().getOrLoadProfileAsync(targetUUID).whenComplete((targetProfile, throwable) -> {
            if (throwable != null) {
                sender.sendMessage(CC.translate("&cFailed to load profile."));
                return;
            }

            ProfilePaperMetadata targetPaperMetadata = targetProfile.getServerMetadata(services, ProfilePaperMetadata.class);

            if (targetProfile.canEquipTag(tag.getName())) {
                sender.sendMessage(CC.translate("&cPlayer already has tag &f" + tag.getDisplayName()));
                return;
            }

            targetProfile.addTag(tag.getName());
            services.getTranslator().send(sender, "TAGS.MENU.TAG.GIVEN_TAG",
                    targetProfile,
                    LocalPlaceholders.builder().add("<tag_name>", tag.getDisplayName()));
            services.getProfileService().saveProfile(targetProfile);

            Player target = services.getPlugin().getServer().getPlayer(targetUUID);
            if (target != null) {
                services.getTranslator().send(target, "TAGS.MENU.TAG.RECEIVED_TAG",
                        sender,
                        LocalPlaceholders.builder().add("<tag_name>", tag.getDisplayName()));
            }
        });
    }

    @Subcommand("take")
    @Syntax("<name> <player>")
    @CommandCompletion("@tags @players")
    @CommandPermission("core.command.tag.manage")
    public void take(Player sender, Tag tag, UUID targetUUID) {
        services.getProfileService().getOrLoadProfileAsync(targetUUID).whenComplete((targetProfile, throwable) -> {
            if (throwable != null) {
                sender.sendMessage(CC.translate("&cFailed to load profile."));
                return;
            }

            if (!targetProfile.canEquipTag(tag.getName())) {
                sender.sendMessage(CC.translate("&cPlayer does not have tag &f" + tag.getDisplayName()));
                return;
            }

            targetProfile.removeTag(tag.getName());
            services.getTranslator().send(sender, "TAGS.MENU.TAG.TAKEN_TAG",
                    targetProfile,
                    LocalPlaceholders.builder().add("<tag_name>", tag.getDisplayName()));
            services.getProfileService().saveProfile(targetProfile);

            Player target = services.getPlugin().getServer().getPlayer(targetUUID);
            if (target != null) {
                services.getTranslator().send(target, "TAGS.MENU.TAG.LOST_TAG",
                        sender,
                        LocalPlaceholders.builder().add("<tag_name>", tag.getDisplayName()));
            }
        });
    }
}
