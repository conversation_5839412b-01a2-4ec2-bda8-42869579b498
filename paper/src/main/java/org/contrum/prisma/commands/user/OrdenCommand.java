package org.contrum.prisma.commands.user;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.entity.Player;
import org.contrum.tritosa.Translator;

@CommandAlias("orden")
public class OrdenCommand extends BaseCommand {

    @Dependency private Translator translator;

    @Default
    public void main(Player player) {
        translator.send(player, "COMMANDS.ORDEN");
    }
}