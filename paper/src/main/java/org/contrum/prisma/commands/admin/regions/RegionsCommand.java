package org.contrum.prisma.commands.admin.regions;

import co.aikar.commands.annotation.*;
import com.sk89q.worldedit.regions.CuboidRegion;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.systems.impl.regionevents.Region;
import org.contrum.prisma.systems.impl.regionevents.RegionEvent;
import org.contrum.prisma.systems.impl.regionevents.RegionEventsSystem;
import org.contrum.prisma.utils.WorldGuardUtils;
import org.contrum.prisma.utils.commands.ToggleableBaseCommand;
import org.contrum.tritosa.Translator;

import java.time.Duration;

@CommandAlias("regions|regionevents") @CommandPermission("core.command.regions")
public class RegionsCommand extends ToggleableBaseCommand {

    @Dependency private RegionEventsSystem system;
    @Dependency private Translator translator;

    @Override
    public boolean isEnabled() {
        return system.isEnabled();
    }

    @Subcommand("list")
    public void listRegions(CommandSender sender) {
        for (Region region : system.getRegions().values()) {
            sender.sendMessage(CC.translate("&e- &f" + region.getName()));
        }
    }

    @Subcommand("create")
    public void createRegion(Player player, String name) {
        CuboidRegion region = WorldGuardUtils.getSelectedRegion(player);
        if (region == null) {
            translator.send(player, "REGION_EVENTS.CREATE.NO_REGION_SELECTED");
            return;
        }

        system.createRegion(name, region);
        translator.send(player, "REGION_EVENTS.CREATE.CREATED");
    }

    @Subcommand("getevents") @CommandCompletion("@regions")
    public void removeEvent(Player player, Region region) {
        player.sendMessage(CC.translate("&aEvent regions: &e" + region.getEvents()));
    }

    @Subcommand("addevent") @CommandCompletion("@regions @region_events")
    public void addEvent(Player player, Region region, String eventName) {
        if (region.getEvents().contains(eventName)) {
            player.sendMessage(CC.translate("&cLa region ya tiene ese evento!"));
            return;
        }

        try {
            region.addEvent(eventName);
        } catch (IllegalArgumentException e) {
            player.sendMessage(CC.translate("&c" + e.getMessage()));
        }
        system.saveRegion(region);
        player.sendMessage(CC.translate("&aEvento agregado!"));
    }

    @Subcommand("removeevent") @CommandCompletion("@regions @region_events")
    public void removeEvent(Player player, Region region, String eventName) {
        if (!region.getEvents().contains(eventName)) {
            player.sendMessage(CC.translate("&cLa region no contiene ese evento!"));
            return;
        }

        region.removeEvent(eventName);
        system.saveRegion(region);
        player.sendMessage(CC.translate("&aEvento removido!"));
    }

    @Subcommand("startEvent") @CommandCompletion("@regions @region_events")
    public void startEvent(Player player, Region region, String eventName) {
        region.startEvent(eventName);
        player.sendMessage("Event started!");
    }

    @Subcommand("stopActiveEvent") @CommandCompletion("@regions")
    public void stopEvent(Player player, Region region) {
        region.finishCurrentEvent(false, RegionEvent.StopReason.COMMAND);
        player.sendMessage("Event stopped!");
    }

    @Subcommand("setZone") @CommandCompletion("@regions")
    public void setZone(Player player, Region region) {

        CuboidRegion rg = WorldGuardUtils.getSelectedRegion(player);
        if (rg == null) {
            player.sendMessage(CC.translate("&cSelecciona una zona primero!"));
            return;
        }

        region.setRegion(rg);
        system.saveRegion(region);
        player.sendMessage(CC.translate("&aZona de la region cambiada!"));
    }

    @Subcommand("setentrancelocation")
    @CommandCompletion("@regions")
    public void setEntranceLocation(Player player, Region region) {
        region.setEntranceLocation(player.getLocation());
        system.saveRegion(region);
        player.sendMessage(CC.translate("&aUbicación de entrada establecida!"));
    }

    @Subcommand("seteventstartcooldown")
    @CommandCompletion("@regions")
    public void setEventStartCooldown(Player player, Region region, Duration duration) {
        region.setEventCooldownDuration(duration);
        system.saveRegion(region);
        player.sendMessage(CC.translate("&aCooldown de inicio de evento establecido!"));
    }

    @Subcommand("seteventduration")
    @CommandCompletion("@regions @region_events")
    public void setEventDuration(Player player, Region region, String event, Duration duration) {
        if (region.setEventDuration(event, duration)) {
            system.saveRegion(region);
            player.sendMessage(CC.translate("&aDuración de evento establecida!"));
        } else {
            player.sendMessage(CC.translate("&cNo se encontró un evento con ese nombre en la región!"));
        }
    }

    @Subcommand("blacklistedcommands add")
    @CommandCompletion("@regions")
    public void addBlacklistedCommand(Player player, Region region, String command) {
        if (command.startsWith("/")) // remove first '/'
            command = command.substring(1);
        region.getBlacklistedCommands().add(command);
        system.saveRegion(region);
        player.sendMessage(CC.translate("&aComando agregado!"));
    }

    @Subcommand("blacklistedcommands remove")
    @CommandCompletion("@regions")
    public void removeBlacklistedCommand(Player player, Region region, String command) {
        if (command.startsWith("/")) // remove first '/'
            command = command.substring(1);
        region.getBlacklistedCommands().remove(command);
        system.saveRegion(region);
        player.sendMessage(CC.translate("&aComando removido!"));
    }

    @Subcommand("blacklistedcommands list")
    @CommandCompletion("@regions")
    public void listBlacklistedCommands(Player player, Region region) {
        player.sendMessage(CC.translate("&aComandos bloqueados: &e" + String.join(", ", region.getBlacklistedCommands())));
    }
}