/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by EnzoLy on 16/04/2024
 * Website: contrum.org
 */

package org.contrum.prisma.commands.user.media;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.commands.user.media.packets.StreamPacket;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.LinkUtils;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.time.Duration;

@CommandAlias("stream|directo|newstream|streaming|newvideo")
@CommandPermission("core.command.stream")
public class StreamCommand extends BaseCommand {

    @Dependency private PaperServices services;
    @Dependency private Translator translator;

    @Default
    public void newVideo(Player player, String link) {
        if(!LinkUtils.isMediaLink(link)) {
            translator.send(player, "COMMANDS.MEDIA.LINK_BAD_FORMAT");
            return;
        }

        Profile profile = services.getProfileService().getProfile(player.getUniqueId());
        ProfilePaperMetadata metadata = profile.getServerMetadata(services, ProfilePaperMetadata.class);

        if(metadata.hasCooldown("stream")) {
            translator.send(player, "COMMANDS.MEDIA.COOLDOWN", player,
                    LocalPlaceholders.builder().add("<cooldown>",
                            metadata.getCooldown("stream").getTimeLeft()));
            return;
        }

        if (!player.hasPermission("core.command.stream.bypass"))
            metadata.setCooldown("stream", Duration.ofMinutes(2).toMillis());

        services.getRedisBackend().sendPacket(new StreamPacket(profile.getRealDisplayName(), link));
    }

}
