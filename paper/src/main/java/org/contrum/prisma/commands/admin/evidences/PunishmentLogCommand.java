package org.contrum.prisma.commands.admin.evidences;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.entity.Player;
import org.contrum.chorpu.TaskUtil;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.staff.evidences.StaffEvidenceService;
import org.contrum.prisma.staff.evidences.punishment.menu.StaffPunishmentsLogMenu;
import org.contrum.tritosa.Translator;

import java.util.UUID;

@CommandAlias("punishlog|punishmentlogs") @CommandPermission("core.command.punishmentlogs")
public class PunishmentLogCommand extends BaseCommand {

    @Dependency private ProfileService profileService;
    @Dependency private StaffEvidenceService staffEvidenceService;
    @Dependency private Translator translator;

    @Default @CommandCompletion("@players")
    @Syntax("<player>")
    public void command(Player sender, UUID target) {
        profileService.getOrLoadProfileAsync(target).whenComplete((profile, ex) -> {
            if (profile == null) {
                translator.send(sender, "ERRORS.COULDNT_FIND_USER");
                return;
            }

            TaskUtil.run(staffEvidenceService.getPlugin(), () -> new StaffPunishmentsLogMenu(translator, staffEvidenceService, profileService, profile).open(sender));
            sender.sendMessage(CC.translate("&eViewing punishments logs of " + profile.getName()));
        }).exceptionally(e -> {
            e.printStackTrace();
            return null;
        });
    }
}
