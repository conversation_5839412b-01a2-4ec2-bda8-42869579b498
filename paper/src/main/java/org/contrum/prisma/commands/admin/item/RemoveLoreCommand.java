/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by txmydev on 28/03/2024
 * Website: contrum.org
 */

package org.contrum.prisma.commands.admin.item;

import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Dependency;
import co.aikar.commands.annotation.Syntax;
import org.bukkit.entity.Player;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

public class RemoveLoreCommand extends ItemCommandHelper{

    @Dependency
    private Translator translator;

    @CommandAlias("removelore")
    @CommandPermission("core.command.removelore")
    @Syntax("<line>")
    public void removelore(Player player, int line) {
        if (modifyAndApply(player, builder -> builder.removeLore(line))) {
            translator.send(player, "COMMANDS.REMOVED_LORE", LocalPlaceholders.builder()
                    .add("<line>", String.valueOf(line)));
        }
    }

}
