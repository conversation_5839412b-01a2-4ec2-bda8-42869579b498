/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by txmydev on 28/03/2024
 * Website: contrum.org
 */

package org.contrum.prisma.commands.admin.item;

import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Syntax;
import org.apache.commons.lang.StringUtils;
import org.bukkit.entity.Player;

public class RenameCommand extends ItemCommandHelper{

    @CommandAlias("rename")
    @CommandPermission("core.command.rename")
    @Syntax("<item name>")
    public void rename(Player player, String[] name) {
        String newName = StringUtils.join(name, " ");

        if (modifyAndApply(player, builder -> builder.setName(newName))) {
            translator.send(player, "COMMANDS.RENAMED_ITEM", newName);
        }
    }

}
