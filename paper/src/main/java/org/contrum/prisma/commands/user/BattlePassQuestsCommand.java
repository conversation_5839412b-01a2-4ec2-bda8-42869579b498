package org.contrum.prisma.commands.user;

import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.systems.impl.battlepass.BattlePassSystem;
import org.contrum.prisma.systems.impl.battlepass.metadata.ProfileBattlePassMetadata;
import org.contrum.prisma.systems.impl.battlepass.quests.menu.BattlePassQuestsMenu;
import org.contrum.prisma.utils.commands.ToggleableBaseCommand;

@CommandAlias("misiones")
public class BattlePassQuestsCommand extends ToggleableBaseCommand {

    @Dependency private PaperServices services;
    @Dependency private BattlePassSystem system;

    @Override
    public boolean isEnabled() {
        return system.isEnabled();
    }

    @Default
    public void menu(Player player) {
        new BattlePassQuestsMenu(system, services.getProfileService().getServerMetadata(player.getUniqueId(), ProfileBattlePassMetadata.class)).open(player);
    }
}
