package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Default;
import org.bukkit.entity.Player;
import org.contrum.chorpu.chat.CC;

@CommandAlias("showme") @CommandPermission("core.command.showme")
public class NoTrackHideCommand extends BaseCommand {
    @Default
    public void toggle(Player player) {
        player.setShowInNoTrack(!player.isShowInNoTrack());
        if (player.isShowInNoTrack()) {
            player.sendMessage(CC.translate("&aAhora todos te veran en el spawn!"));
        } else {
            player.sendMessage(CC.translate("&cYa nadie te verá en el spawn!"));
        }
    }
}
