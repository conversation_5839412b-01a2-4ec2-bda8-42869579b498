package org.contrum.prisma.commands.user;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.bukkit.entity.Player;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.trading.TradeRequestResult;
import org.contrum.prisma.trading.TradeService;
import org.contrum.tritosa.Translator;

import javax.inject.Inject;

@CommandAlias("trade")
public class TradeCommand extends BaseCommand {

    @Dependency
    private TradeService tradeService;

    @Dependency
    private Translator translator;

    @Dependency
    private ProfileService profileService;

    @Default
    @Description("Send a trade request to a player")
    @Syntax("<target>")
    public void tradeCommand(Player sender, @Optional @Flags("other") OnlinePlayer target) {
        if (target == null) {
            translator.send(sender, "TRADE.MESSAGES.USAGE");
            return;
        }

        Player targetPlayer = target.getPlayer();

        TradeRequestResult result = tradeService.sendTradeRequest(sender, targetPlayer);

        switch (result) {
            case SUCCESS -> {
                // The TradeService already handles sending the success messages
            }
            case TARGET_IGNORING -> translator.send(sender, "TRADE.MESSAGES.TARGET_IGNORING");
            case ALREADY_HAS_PENDING_REQUEST -> translator.send(sender, "TRADE.MESSAGES.REQUEST_FAILED");
            case SELF_REQUEST -> translator.send(sender, "TRADE.MESSAGES.SELF");
        }
    }

    @Subcommand("accept")
    @Description("Accept a pending trade request")
    public void acceptTrade(Player player) {
        boolean accepted = tradeService.acceptTradeRequest(player);

        if (!accepted) {
            translator.send(player, "TRADE.MESSAGES.NO_PENDING_REQUEST");
        }
        // The TradeService handles sending the success messages
    }

    @Subcommand("decline")
    @Description("Decline a pending trade request")
    public void declineTrade(Player player) {
        if (!tradeService.hasPendingRequest(player)) {
            translator.send(player, "TRADE.MESSAGES.NO_PENDING_REQUEST");
            return;
        }
        tradeService.declineTrade(player);
        // The TradeService handles sending the decline message
    }

    @Subcommand("ignore")
    @Description("Toggle ignoring trade requests")
    public void ignoreTrades(Player player) {
        var profile = profileService.getProfile(player.getUniqueId());
        if (profile == null) {
            translator.send(player, "TRADE.MESSAGES.PROFILE_NOT_FOUND");
            return;
        }

        boolean currentlyIgnoring = profile.isTradeIgnore();
        profile.setTradeIgnore(!currentlyIgnoring);


        if (profile.isTradeIgnore()) {
            translator.send(player, "TRADE.MESSAGES.IGNORE_ON");
            if (tradeService.hasPendingRequest(player)) {
                tradeService.declineTrade(player);
            }
        } else {
            translator.send(player, "TRADE.MESSAGES.IGNORE_OFF");
        }
    }
}