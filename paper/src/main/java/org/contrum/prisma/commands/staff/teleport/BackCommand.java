package org.contrum.prisma.commands.staff.teleport;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandPermission;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.entity.Player;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.tritosa.Translator;

@CommandAlias("back")
@CommandPermission("core.command.back")
public class BackCommand extends BaseCommand {

    @Dependency private ProfileService profileService;
    @Dependency private Translator translator;

    @Default
    public void back(Player player) {
        Profile profile = profileService.getProfile(player.getUniqueId());

        ProfilePaperMetadata metadata = profile.getServerMetadata(profileService, ProfilePaperMetadata.class);

        if (metadata.getLastLocation() == null) {
            translator.send(player, "COMMANDS.BACK.NO_LOCATION");
            return;
        }

        player.teleport(metadata.getLastLocation());
        metadata.setLastLocation(null);
        translator.send(player, "COMMANDS.BACK.TELEPORTED");
    }

}
