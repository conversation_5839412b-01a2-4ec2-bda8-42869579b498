/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by txmydev on 28/03/2024
 * Website: contrum.org
 */

package org.contrum.prisma.commands.user.messaging;

import co.aikar.commands.annotation.*;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.punishment.Punishment;
import org.contrum.prisma.punishment.PunishmentType;
import org.contrum.tritosa.Translator;

import java.util.Optional;

public class PrivateMessageCommand extends MessageCommand {

    @Dependency
    private PaperServices services;
    @Dependency
    private Translator translator;

    @CommandAlias("msg|tell|messageplayer|m|t|whisper|w")
    @Syntax("<player> <message>")
    @CommandCompletion("@onlineplayersbutself")
    public void message(Player player,
                        @Flags("onlyDisplayForUsers") Profile profile,
                        String[] message) {
        ProfilePaperMetadata metadata = profile.getServerMetadata(services, ProfilePaperMetadata.class);
        Profile sender = services.getProfileService().getProfile(player.getUniqueId());

        if (sender.equals(profile)) {
            translator.send(player, "ERRORS.MESSAGE_TO_SELF");
            return;
        }

        Optional<Punishment> mute = profile.getActivePunishmentByType(PunishmentType.MUTE);

        if (mute.isPresent()) {
            translator.send(sender, "ERRORS.USER_MUTED", mute.get());
            return;
        }

        profile.setChatter(sender);
        sender.setChatter(profile);

        message(translator, player, sender, profile, message);
    }

}
