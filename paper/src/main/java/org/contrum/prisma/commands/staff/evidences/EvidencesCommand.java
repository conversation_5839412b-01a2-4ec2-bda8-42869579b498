package org.contrum.prisma.commands.staff.evidences;

import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.CommandCompletion;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.entity.Player;
import org.contrum.chorpu.TaskUtil;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.staff.evidences.StaffEvidenceService;
import org.contrum.prisma.staff.evidences.menu.StaffEvidenceMenu;
import org.contrum.prisma.utils.commands.ToggleableBaseCommand;
import org.contrum.tritosa.Translator;

import java.util.UUID;

@CommandAlias("evidences|evidencias")
public class EvidencesCommand extends ToggleableBaseCommand {

    @Dependency private Translator translator;
    @Dependency private StaffEvidenceService staffEvidenceService;
    @Dependency private ProfileService profileService;

    @Default
    public void evidence(Player player) {
        new StaffEvidenceMenu(staffEvidenceService, profileService.getProfile(player.getUniqueId())).open(player);
    }

    @Default @CommandCompletion("@players")
    public void evidenceOther(Player player, UUID target) {
        profileService.getOrLoadProfileAsync(target).whenComplete((profile, ex) -> {
            if (profile == null) {
                translator.send(player, "ERRORS.COULDNT_FIND_USER");
                return;
            }

            TaskUtil.run(staffEvidenceService.getPlugin(), () -> new StaffEvidenceMenu(staffEvidenceService, profile).open(player));
            player.sendMessage(CC.translate("&eViewing evidences of " + profile.getName()));
        }).exceptionally(e -> {
            e.printStackTrace();
            return null;
        });
    }

    @Override
    public boolean isEnabled() {
        return PrismaCoreSetting.STAFF_EVIDENCES_ENABLED;
    }
}
