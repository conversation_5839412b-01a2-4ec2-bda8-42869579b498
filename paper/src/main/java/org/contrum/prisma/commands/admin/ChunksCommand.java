package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.Bukkit;
import org.bukkit.World;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.contrum.chorpu.chat.CC;

@CommandAlias("chunks|chunk") @CommandPermission("core.command.chunks")
public class ChunksCommand extends BaseCommand {

    @Default @HelpCommand
    public void help(CommandSender sender) {
        sender.sendMessage("  ");
        sender.sendMessage(CC.translate("&b&lChunks Commands"));
        sender.sendMessage(CC.translate("&7/chunks getcurrent [world]"));
        sender.sendMessage(CC.translate("&7/chunks setviewdistance <amount> [world|global]"));
        sender.sendMessage(CC.translate("&7/chunks setsimulationdistance <amount> [world|global]"));
        sender.sendMessage("     ");
    }

    @Subcommand("getcurrent") @Syntax("[world]")
    @CommandCompletion("@worlds")
    public void getView(CommandSender sender, @Optional String world) {
        if (world == null || world.isEmpty()) {
            if (sender instanceof Player player) {
                world = player.getWorld().getName();
            } else {
                world = "world";
            }
        }

        World w = Bukkit.getWorld(world);
        if (w == null) {
            sender.sendMessage(CC.translate("&cWorld doesn't exist!"));
            return;
        }

        sender.sendMessage(CC.translate("&eRender distance of world: &c" + world));
        sender.sendMessage(CC.translate("&eViewDistance: &c" + w.getViewDistance()));
        sender.sendMessage(CC.translate("&eSimulationDistance: &c" + w.getSimulationDistance()));
    }

    @Subcommand("setviewdistance") @Syntax("<amount> [world|global]")
    @CommandCompletion("@range:2-32 @worlds|global")
    public void setView(CommandSender sender, int view, @Optional String world) {
        if (world == null || world.isEmpty()) {
            if (sender instanceof Player player) {
                world = player.getWorld().getName();
            } else {
                world = "world";
            }
        }

        if (world.equalsIgnoreCase("global") || world.equalsIgnoreCase("all")) {
            for (World w : Bukkit.getWorlds()) {
                w.setViewDistance(view);
            }
            sender.sendMessage(CC.translate("&eView distance has been set to &c" + view + "&e in all worlds!"));
            return;
        }

        World w = Bukkit.getWorld(world);
        if (w == null) {
            sender.sendMessage(CC.translate("&cWorld doesn't exist!"));
            return;
        }

        w.setViewDistance(view);
        sender.sendMessage(CC.translate("&eView distance has been set to &c" + view + "&e in &c" + world));
    }

    @Subcommand("setsimulationdistance") @Syntax("<amount> [world|global]")
    @CommandCompletion("@range:2-32 @worlds|global")
    public void setSimulation(CommandSender sender, int view, @Optional String world) {
        if (world == null || world.isEmpty()) {
            if (sender instanceof Player player) {
                world = player.getWorld().getName();
            } else {
                world = "world";
            }
        }

        if (world.equalsIgnoreCase("global") || world.equalsIgnoreCase("all")) {
            for (World w : Bukkit.getWorlds()) {
                w.setSimulationDistance(view);
            }
            sender.sendMessage(CC.translate("&eSimulation distance has been set to &c" + view + "&e in all worlds!"));
            return;
        }

        World w = Bukkit.getWorld(world);
        if (w == null) {
            sender.sendMessage(CC.translate("&cWorld doesn't exist!"));
            return;
        }

        w.setSimulationDistance(view);
        sender.sendMessage(CC.translate("&eSimulation distance has been set to &c" + view + "&e in &c" + world));
    }
}
