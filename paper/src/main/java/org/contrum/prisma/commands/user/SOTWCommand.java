package org.contrum.prisma.commands.user;

import co.aikar.commands.annotation.*;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.systems.impl.SOTWSystem;
import org.contrum.prisma.utils.commands.ToggleableBaseCommand;
import org.contrum.tritosa.Translator;

import java.time.Duration;

@CommandAlias("sotw")
public class SOTWCommand extends ToggleableBaseCommand {

    @Dependency private PaperServices services;
    @Dependency private Translator translator;
    @Dependency private SOTWSystem sotwSystem;

    @Override
    public boolean isEnabled() {
        return sotwSystem.isEnabled();
    }

    @Subcommand("start") @CommandPermission("core.command.sotw.manage")
    public void start(CommandSender sender, Duration duration) {
        sotwSystem.startSOTW(duration);
    }

    @Subcommand("disable")
    public void disable(Player player) {
        if (sotwSystem.disableSOTWFor(player)) {
            translator.send(player, "SOTW.DISABLED");
        } else {
            translator.send(player, "SOTW.ALREADY_DISABLED");
        }
    }

    @Subcommand("enable") @CommandPermission("core.command.sotw.manage") @CommandCompletion("@players")
    public void enable(CommandSender player, OnlinePlayer target) {
        sotwSystem.enableSOTWFor(target.getPlayer(), true);
    }
}
