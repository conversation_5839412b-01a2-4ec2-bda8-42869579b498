package org.contrum.prisma.commands.staff.punishment.commands.punish;

import co.aikar.commands.annotation.*;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.contrum.prisma.commands.staff.punishment.PunishmentCommand;
import org.contrum.prisma.punishment.PunishmentType;
import org.contrum.prisma.utils.ServerContext;

import java.util.UUID;

@CommandAlias("warn")
@CommandPermission("core.command.warn")
public class WarnCommand extends PunishmentCommand {

    @Default
    @Syntax("<player> <reason>")
    @CommandCompletion("@onlineplayersbutself")
    public void warn(CommandSender sender, UUID target, String[] reasonRaw) {
        this.punish(PunishmentType.WARN, sender, target, ServerContext.GLOBAL, null, reasonRaw);
    }
}