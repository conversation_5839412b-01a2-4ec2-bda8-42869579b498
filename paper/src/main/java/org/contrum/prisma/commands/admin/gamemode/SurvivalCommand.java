/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by txmydev on 28/03/2024
 * Website: contrum.org
 */

package org.contrum.prisma.commands.admin.gamemode;

import co.aikar.commands.annotation.*;
import org.bukkit.GameMode;
import org.bukkit.entity.Player;

public class SurvivalCommand extends GamemodeCommandHelper {


    @CommandAlias("gms|gm0|survival|gamemodesurvival")
    @CommandPermission("core.command.gms")
    @Description("Set's your gamemode to survival.")
    @Syntax("[player]")
    @CommandCompletion("@players")
    public void gmc(Player player, @Optional String otherName) {
        perform(player, otherName, GameMode.SURVIVAL);
    }
}
