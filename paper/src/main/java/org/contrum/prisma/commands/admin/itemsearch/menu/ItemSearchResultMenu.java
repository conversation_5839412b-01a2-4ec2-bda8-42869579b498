package org.contrum.prisma.commands.admin.itemsearch.menu;

import lombok.RequiredArgsConstructor;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.PaginatedMenu;
import org.contrum.chorpu.menu.storage.StorageMenu;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.security.SecurityService;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.time.TimeUtils;

import java.util.*;
import java.util.stream.Collectors;

public class ItemSearchResultMenu extends PaginatedMenu {

    private PaperServices services;
    private SecurityService.PlayerSearchFilter filter;
    private HashMap<UUID, SecurityService.PlayerSearchData> data;

    private final List<Button> buttons = new ArrayList<>();

    public ItemSearchResultMenu(PaperServices services, SecurityService.PlayerSearchFilter filter, HashMap<UUID, SecurityService.PlayerSearchData> data) {
        try {
            this.services = services;
            this.filter = filter;
            this.data = data;

            List<Map.Entry<UUID, SecurityService.PlayerSearchData>> entries = data.entrySet().stream().sorted(new DataComparator(services)).toList();
            for (Map.Entry<UUID, SecurityService.PlayerSearchData> entry : entries) {
                UUID uuid = entry.getKey();
                SecurityService.PlayerSearchData searchData = entry.getValue();

                if (searchData == null) continue;

                if (searchData.getFilter().getAmount() > searchData.getTotal()) continue;

                Profile profile = services.getProfileService().getOrLoadProfile(uuid);
                OfflinePlayer player = Bukkit.getOfflinePlayer(uuid);

                ItemBuilder1_20 builder = new ItemBuilder1_20(Material.PLAYER_HEAD);
                builder.setOwner(player.getName());
                builder.name(player.getName());

                builder.addLore("&eOnline: &c" + services.getProfileService().isOnline(profile));
                builder.addLore("&ePunished: &c" + profile.isPunished());
                builder.addLore("&eLast join: &c" + TimeUtils.getFormattedTime(System.currentTimeMillis() - profile.getGlobalLastSeen()));
                builder.addLore("&e");
                builder.addLore("&eTotal: " + searchData.getTotal());
                builder.addLore("&eInventory: " + searchData.get(SecurityService.PlayerSearchData.Type.INVENTORY));
                builder.addLore("&eEnderchest: " + searchData.get(SecurityService.PlayerSearchData.Type.ENDER_CHEST));
                builder.addLore("&eVault: " + searchData.get(SecurityService.PlayerSearchData.Type.PLAYER_VAULT));

                buttons.add(Button.of(builder.build(), (c) -> {
                    new PlayerSearchDataMenu(services, profile, searchData, data).setBack(this::open).open(c);
                }));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public List<Button> getPaginatedButtons(Player player) {
        return buttons;
    }

    @Override
    public Map<Integer, Button> getGlobalButtons(Player player) {
        return Map.of();
    }

    @Override
    public int getRows(Player player) {
        return 6;
    }

    @Override
    public String getTitle(Player player) {
        return "Search for " + filter.getItem().getType();
    }

    @Override
    public StorageMenu.FillType getFillType() {
        return StorageMenu.FillType.ONLY_CORNERS;
    }

    @Override
    public boolean isAutoUpdate() {
        return false;
    }

    @RequiredArgsConstructor
    private class DataComparator implements java.util.Comparator<Map.Entry<UUID, SecurityService.PlayerSearchData>> {

        private final PaperServices services;

        @Override
        public int compare(Map.Entry<UUID, SecurityService.PlayerSearchData> e1, Map.Entry<UUID, SecurityService.PlayerSearchData> e2) {
            UUID u1 = e1.getKey();
            UUID u2 = e2.getKey();
            SecurityService.PlayerSearchData s1 = e1.getValue();
            SecurityService.PlayerSearchData s2 = e2.getValue();

            return Integer.compare(s2.getTotal(), s1.getTotal());
        }
    }
}
