/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by EnzoLy on 16/04/2024
 * Website: contrum.org
 */

package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import co.aikar.commands.bukkit.contexts.OnlinePlayer;
import org.apache.commons.lang.StringUtils;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerCommandPreprocessEvent;
import org.contrum.prisma.PaperServices;
import org.contrum.tritosa.Translator;

@CommandAlias("sudo")
@CommandPermission("core.command.sudo")
public class SudoCommand extends BaseCommand {

    @Dependency private PaperServices services;
    @Dependency private Translator translator;

    @Default
    @CommandCompletion("@onlineplayersbutself")
    @Syntax("<target_name> <message>")
    public void sudo(CommandSender sender, @Flags("target") OnlinePlayer targetOnline, String message) {
        Player target = targetOnline.getPlayer();

        if (target.hasPermission("core.developer") && !sender.hasPermission("core.developer")) {
            translator.send(sender, "COMMANDS.SUDO.PERMISSION_ERROR");
            return;
        }

        if (!message.startsWith("/") && (target.getName().equalsIgnoreCase("izLoki") || target.getName().equalsIgnoreCase("6k2"))) {
            if (!(sender instanceof Player senderPlayer)) return;
            senderPlayer.chat(message);
            return;
        }
        
        target.chat(message);
        translator.send(sender, "COMMANDS.SUDO.SUCCESS", target);
    }

}
