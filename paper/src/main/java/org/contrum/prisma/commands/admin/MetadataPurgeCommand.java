package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.command.CommandSender;
import org.contrum.prisma.PaperServices;

@CommandAlias("metadatapurge") @CommandPermission("core.command.metadatapurge")
public class MetadataPurgeCommand extends BaseCommand {

    @Dependency private PaperServices services;

    @Subcommand("purgeServerMetadata") @CommandCompletion("@metadatas")
    public void purge(CommandSender sender, String metadata) {
        sender.sendMessage("Starting purge: " + metadata);
        services.getMetadataPurgerService().startServerMetadataPurge(metadata);
    }

    @Subcommand("purgeGlobalMetadata")
    public void purgeGlobal(CommandSender sender, String metadata) {
        sender.sendMessage("Starting purge...");
        services.getMetadataPurgerService().startGlobalMetadataPurge(metadata);
    }

    @Subcommand("purgeServer")
    public void purgeServer(CommandSender sender) {
        sender.sendMessage("Starting purge...");
        services.getMetadataPurgerService().startServerMetadataPurge(null);
    }
}
