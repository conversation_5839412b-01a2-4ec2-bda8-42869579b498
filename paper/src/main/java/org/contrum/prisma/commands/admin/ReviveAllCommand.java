package org.contrum.prisma.commands.admin;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.command.CommandSender;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.profile.log.ProfileLogService;

import java.time.Duration;
import java.time.Instant;

@CommandAlias("reviveall") @CommandPermission("core.command.reviveall")
public class ReviveAllCommand extends BaseCommand {

    @Dependency private ProfileLogService service;

    @Default
    public void revive(CommandSender sender, Duration start, Duration end) {
        Instant startTime = Instant.now().minus(start);
        Instant endTime = Instant.now().minus(end);

        service.getDeathsRestoreManager().setRestoreAllowed(startTime, endTime);
        service.getDeathsRestoreManager().broadcastRestore();
    }

    @Subcommand("disable")
    public void disable(CommandSender sender) {
        service.getDeathsRestoreManager().disableRestore();
        sender.sendMessage(CC.translate("&cRestore disabled."));
    }
}
