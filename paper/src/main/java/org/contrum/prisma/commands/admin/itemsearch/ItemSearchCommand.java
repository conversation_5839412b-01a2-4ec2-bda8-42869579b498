package org.contrum.prisma.commands.admin.itemsearch;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.TaskUtil;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.commands.admin.itemsearch.menu.ItemSearchResultMenu;
import org.contrum.prisma.security.SecurityService;
import org.contrum.tritosa.Translator;

import java.util.HashMap;
import java.util.UUID;

@CommandAlias("searchitem|itemsearch") @CommandPermission("core.command.searchitems")
public class ItemSearchCommand extends BaseCommand {

    @Dependency private Translator translator;
    @Dependency private PaperServices services;
    @Dependency private SecurityService securityService;

    @Default
    @Syntax("[amount] [offlineSearch]")
    @CommandCompletion("@range:1-200 true|false")
    public void searchItem(Player player, int amountFilter, @Optional boolean offlineSearch) {
        ItemStack item = player.getInventory().getItemInMainHand();
        if (item.getType().equals(Material.AIR)) {
            translator.send(player, "ERRORS.INVALID_ITEM");
            return;
        }

        player.sendMessage(CC.translate("&aPerforming async search..."));
        TaskUtil.runAsync(services.getPlugin(), () -> {
            try {
                SecurityService.PlayerSearchFilter filter = new SecurityService.PlayerSearchFilter(item);
                filter.setAmount(amountFilter);

                HashMap<UUID, SecurityService.PlayerSearchData> data = securityService.performPlayerInventorySearch(filter, offlineSearch);

                player.sendMessage(CC.translate("&aSorting menu..."));
                ItemSearchResultMenu menu = new ItemSearchResultMenu(services, filter, data);
                player.sendMessage(CC.translate("&aLoading..."));
                TaskUtil.run(services.getPlugin(), () -> {
                    menu.open(player);
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    @Subcommand("bytype")
    @Syntax("[material] [amount] [offlineSearch]")
    @CommandCompletion("@materials @range:1-200 true|false")
    public void searchItem(Player player, Material material, int amountFilter, @Optional boolean offlineSearch) {
        if (material.equals(Material.AIR)) {
            translator.send(player, "ERRORS.INVALID_ITEM");
            return;
        }

        player.sendMessage(CC.translate("&aPerforming async search..."));
        TaskUtil.runAsync(services.getPlugin(), () -> {
            try {
                SecurityService.PlayerSearchFilter filter = new SecurityService.PlayerSearchFilter(material, null);
                filter.setAmount(amountFilter);

                HashMap<UUID, SecurityService.PlayerSearchData> data = securityService.performPlayerInventorySearch(filter, offlineSearch);

                player.sendMessage(CC.translate("&aSorting menu..."));
                ItemSearchResultMenu menu = new ItemSearchResultMenu(services, filter, data);
                player.sendMessage(CC.translate("&aLoading..."));
                TaskUtil.run(services.getPlugin(), () -> {
                    menu.open(player);
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }
}