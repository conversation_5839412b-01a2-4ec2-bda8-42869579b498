package org.contrum.prisma.economy.baltop;

import lombok.AllArgsConstructor;
import lombok.Data;
import org.contrum.prisma.profile.currency.Currency;

import java.util.UUID;

@Data
@AllArgsConstructor
public class PlayerBalance implements Comparable<PlayerBalance> {
    private UUID uuid;
    private String name;
    private long balance;
    private Currency currency;

    @Override
    public int compareTo(PlayerBalance other) {
        return Long.compare(other.balance, this.balance);
    }
}