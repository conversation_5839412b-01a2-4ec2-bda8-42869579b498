package org.contrum.prisma.events;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.profile.currency.Currency;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Getter @Setter
public class CurrencyAddEvent extends Event implements Cancellable {
    private static final HandlerList handlers = new HandlerList();
    private boolean cancelled = false;

    @Nullable
    private final Player player;
    private final Currency currency;
    private final Cause cause;
    private long amount;
    public CurrencyAddEvent(@Nullable Player player, Currency currency, Cause cause, long amount){
        this.player = player;
        this.currency = currency;
        this.cause = cause;
        this.amount = amount;
    }

    @Override
    public boolean isCancelled() {
        return cancelled;
    }

    @Override
    public void setCancelled(boolean b) {
        cancelled = b;
    }

    @Override
    public @NotNull HandlerList getHandlers() {
        return handlers;
    }
    public static HandlerList getHandlerList() {
        return handlers;
    }

    public enum Cause {
        UNKNOWN,
        COMMAND,
        KILL,
        BLOCK_BREAK,
        COINS_EXCAVATOR,
        DUEL_BET,
        COIN_FLIP_REFUND,
        COIN_FLIP,
        REGIONS_EVENT,
        TRADE_COMPLETED,
        TRADE_CANCELLED;
        //When the cause is TRADE_CANCELLED essentially no coins were gained, the user just got back the coins he intended to trade in a TradeSession
    }
}
