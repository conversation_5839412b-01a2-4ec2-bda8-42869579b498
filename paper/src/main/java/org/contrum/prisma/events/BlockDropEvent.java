package org.contrum.prisma.events;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.Cancellable;
import org.bukkit.event.HandlerList;
import org.bukkit.event.player.PlayerEvent;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.customarmor.CustomArmor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

@Getter
public class BlockDropEvent extends PlayerEvent implements Cancellable {
    private static final HandlerList handlers = new HandlerList();
    private boolean cancelled = false;

    @Setter
    private ItemStack item;
    private Block block;
    private int amount;

    @Nullable @Setter
    private CustomArmor armor;

    public BlockDropEvent(Player player, ItemStack item, Block block, int amount){
        super(player);
        this.item = item;
        this.block = block;
        this.amount = amount;
    }

    public void setAmount(int amount){
        this.amount = amount;
        this.getItem().setAmount(amount);
    }

    @Override
    public boolean isCancelled() {
        return cancelled;
    }

    @Override
    public void setCancelled(boolean b) {
        cancelled = b;
    }

    @Override
    public @NotNull HandlerList getHandlers() {
        return handlers;
    }

    public static HandlerList getHandlerList() {
        return handlers;
    }
}
