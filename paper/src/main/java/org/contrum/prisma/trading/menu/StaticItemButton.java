package org.contrum.prisma.trading.menu;

import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.menu.button.Button;

public class StaticItemButton extends Button {
        private final ItemStack item;

        public StaticItemButton(ItemStack item) {
            this.item = item.clone();
        }

        @Override
        public ItemStack getDisplayItem(Player player) {
            return item;
        }
    }