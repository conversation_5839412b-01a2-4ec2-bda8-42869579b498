package org.contrum.prisma.trading.events;

import org.bukkit.event.HandlerList;
import org.contrum.prisma.trading.TradeSession;

public class TradeStartEvent extends TradeEvent {

    private static final HandlerList handlers = new HandlerList();

    public TradeStartEvent(TradeSession session) {
        super(session);
    }

    @Override
    public HandlerList getHandlers() {
        return handlers;
    }

    public static HandlerList getHandlerList() {
        return handlers;
    }
}