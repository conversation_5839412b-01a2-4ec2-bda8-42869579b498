package org.contrum.prisma.trading.menu;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.Menu;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.trading.TradeService;
import org.contrum.prisma.trading.TradeSession;
import org.contrum.tritosa.Translator;

import java.util.*;

@RequiredArgsConstructor
@Getter
public class TradeMenu extends Menu {
    private final PaperServices paperServices;
    private final Translator translator;

    private final TradeSession tradeSession;
    private static final int[] DIVIDER_SLOTS = {4, 13, 22, 31, 40};
    private static final int INPUT_SLOT = 39;
    private static final int COIN_TRADE_SLOT = 36;

    private static final int CONFIRMATION_STATUS_SLOT = 41;

    public TradeMenu(TradeSession tradeSession, PaperServices paperServices){
        this.tradeSession = tradeSession;
        this.paperServices = paperServices;
        this.translator = this.paperServices.getTranslator();
    }


    private static final List<Integer> LEFT_SLOTS = Arrays.asList(
            0, 1, 2, 3,
            9, 10, 11, 12,
            18, 19, 20, 21,
            27, 28, 29, 30
    );

    private static final List<Integer> RIGHT_SLOTS = Arrays.asList(
            5, 6, 7, 8,
            14, 15, 16, 17,
            23, 24, 25, 26,
            32, 33, 34, 35
    );

    @Override
    public String getTitle(Player player) {

        UUID otherUUID = tradeSession.getOther(player.getUniqueId());
        Player other = Bukkit.getPlayer(otherUUID);
        return other != null ? other.getName() : "Unknown" + "⇄" + player.getName();
    }

    @Override
    public int getRows(Player player) {
        return 5;
    }

    @Override
    public Map<Integer, Button> getButtons(Player player) {
        Map<Integer, Button> buttons = new HashMap<>();

        addDividerButtons(buttons);
        addItemButtons(buttons, player);
        addInputButton(buttons, player);
        addConfirmationStatusButton(buttons, player);
        addCoinTradeButton(buttons, player);

        return buttons;
    }

    private void addDividerButtons(Map<Integer, Button> buttons) {
        ItemStack divider = new ItemBuilder(Material.BLACK_STAINED_GLASS_PANE)
                .setName(" ")
                .build();

        for (int slot : DIVIDER_SLOTS) {
            buttons.put(slot, new StaticItemButton(divider));
        }
    }

    private void addItemButtons(Map<Integer, Button> buttons, Player player) {
        UUID playerId = player.getUniqueId();
        List<ItemStack> myItems = tradeSession.getPlayerItems(playerId);
        List<ItemStack> otherItems = tradeSession.getOtherPlayerItems(playerId);

        addMyItemButtons(buttons, myItems, playerId);
        addOtherItemButtons(buttons, otherItems);
    }

    private void addMyItemButtons(Map<Integer, Button> buttons, List<ItemStack> myItems, UUID playerId) {
        for (int i = 0; i < myItems.size() && i < LEFT_SLOTS.size(); i++) {
            int itemIndex = i;
            buttons.put(LEFT_SLOTS.get(i), new ClickableItemButton(tradeSession, paperServices, myItems.get(i), itemIndex));
        }
    }

    private void addOtherItemButtons(Map<Integer, Button> buttons, List<ItemStack> otherItems) {
        for (int i = 0; i < otherItems.size() && i < RIGHT_SLOTS.size(); i++) {
            buttons.put(RIGHT_SLOTS.get(i), new StaticItemButton(otherItems.get(i)));
        }
    }



    private void addInputButton(Map<Integer, Button> buttons, Player player) {
        buttons.put(INPUT_SLOT, new InputButton(tradeSession, paperServices));
    }

    private void addConfirmationStatusButton(Map<Integer, Button> buttons, Player player) {
        buttons.put(CONFIRMATION_STATUS_SLOT, new ConfirmationStatusButton(tradeSession, paperServices));
    }
    private void addCoinTradeButton(Map<Integer, Button> buttons, Player player) {
        buttons.put(COIN_TRADE_SLOT, new CoinTradeButton(tradeSession, paperServices, paperServices.getTradeService()));
    }




}