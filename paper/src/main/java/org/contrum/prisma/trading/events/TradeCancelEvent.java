package org.contrum.prisma.trading.events;

import org.bukkit.event.HandlerList;
import org.contrum.prisma.trading.TradeSession;

public class TradeCancelEvent extends TradeEvent {

    private static final HandlerList handlers = new HandlerList();

    public TradeCancelEvent(TradeSession session) {
        super(session);
    }

    @Override
    public HandlerList getHandlers() {
        return handlers;
    }

    public static HandlerList getHandlerList() {
        return handlers;
    }
}