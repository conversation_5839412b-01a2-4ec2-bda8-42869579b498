package org.contrum.prisma.trading.menu;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.trading.TradeSession;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.List;
import java.util.UUID;

public class ConfirmationStatusButton extends Button {

    private final TradeSession session;
    private final PaperServices paperServices;
    private final Translator translator;

    public ConfirmationStatusButton(TradeSession session, PaperServices paperServices) {
        this.session = session;
        this.paperServices = paperServices;
        this.translator = paperServices.getTranslator();
    }

    @Override
    public ItemStack getDisplayItem(Player viewer) {
        UUID viewerId = viewer.getUniqueId();
        UUID partnerId = session.getOther(viewerId);

        Player partner = Bukkit.getPlayer(partnerId);
        String partnerName = partner != null ? partner.getName() : "Unknown";

        boolean partnerConfirmed = session.hasConfirmed(partnerId);

        Material material = partnerConfirmed ? Material.LIME_DYE : Material.GRAY_DYE;
        String baseKey = partnerConfirmed ? "TRADE.MENU.CONFIRMATION_STATUS_BUTTON.CONFIRMED" : "TRADE.MENU.CONFIRMATION_STATUS_BUTTON.PENDING";

        // Use translator to get display name and lore, replacing <other> placeholder with partnerName

        List<String> lore = translator.getListString(viewer, baseKey + ".LORE", LocalPlaceholders.builder()
                .add("<other>", partnerName));
        String displayName = translator.getAsText(viewer, baseKey + ".DISPLAY_NAME", LocalPlaceholders.builder()
                .add("<other>", partnerName));

        return new ItemBuilder(material)
                .setName(displayName)
                .setLore(lore)
                .build();
    }
}