package org.contrum.prisma.trading;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.bson.Document;
import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.trading.menu.TradeMenu;

import java.util.*;
import java.util.stream.Collectors;
@Getter
@Setter
@RequiredArgsConstructor
public class TradeSession {

    private final UUID sender;
    private final UUID target;

    private boolean senderConfirmed = false;
    private boolean targetConfirmed = false;

    private final List<ItemStack> senderItems = new ArrayList<>();
    private final List<ItemStack> targetItems = new ArrayList<>();

    private int countdown = 4;
    private long completedAt = 0L;

    public static final int MAX_ITEMS_PER_PLAYER = 16;

    public UUID getOther(UUID playerId) {
        return playerId.equals(sender) ? target : (playerId.equals(target) ? sender : null);
    }

    public List<ItemStack> getPlayerItems(UUID playerId) {
        if (playerId.equals(sender)) return senderItems;
        if (playerId.equals(target)) return targetItems;
        return Collections.emptyList();
    }


    public List<ItemStack> getOtherPlayerItems(UUID playerId) {
        if (playerId.equals(sender)) return targetItems;
        if (playerId.equals(target)) return senderItems;
        return Collections.emptyList();
    }

    public void toggleConfirmation(UUID playerId) {
        if (countdown > 1) return;
        if (playerId.equals(sender)) {
            senderConfirmed = !senderConfirmed;
        } else if (playerId.equals(target)) {
            targetConfirmed = !targetConfirmed;
        }
    }

    public boolean bothConfirmed() {
        return senderConfirmed && targetConfirmed;
    }

    public boolean hasConfirmed(UUID playerId) {
        if (playerId.equals(sender)) return senderConfirmed;
        if (playerId.equals(target)) return targetConfirmed;
        return false;
    }



    public boolean isCompleted() {
        return completedAt > 0L;
    }

    public Document toBson() {
        return new Document()
                .append("sender", sender.toString())
                .append("target", target.toString())
                .append("senderConfirmed", senderConfirmed)
                .append("targetConfirmed", targetConfirmed)
                .append("senderItems", senderItems.stream().map(ItemUtil::itemStackToBase64).toList())
                .append("targetItems", targetItems.stream().map(ItemUtil::itemStackToBase64).toList())
                .append("completedAt", completedAt);
    }

    public static TradeSession fromBson(Document doc) {
        TradeSession session = new TradeSession(
                UUID.fromString(doc.getString("sender")),
                UUID.fromString(doc.getString("target")));
        session.setSenderConfirmed(doc.getBoolean("senderConfirmed", false));
        session.setTargetConfirmed(doc.getBoolean("targetConfirmed", false));
        session.setCompletedAt(doc.getLong("completedAt"));

        doc.getList("senderItems", String.class, List.of()).forEach(base64 ->
                session.getSenderItems().add(ItemUtil.itemStackFromBase64(base64)));
        doc.getList("targetItems", String.class, List.of()).forEach(base64 ->
                session.getTargetItems().add(ItemUtil.itemStackFromBase64(base64)));

        return session;
    }
}
