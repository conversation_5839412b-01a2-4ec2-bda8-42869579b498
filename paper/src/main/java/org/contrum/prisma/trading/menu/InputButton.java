package org.contrum.prisma.trading.menu;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.trading.TradeSession;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.List;
import java.util.Map;
import java.util.UUID;

public class InputButton extends Button {

    private final PaperServices paperServices;
    private final TradeSession session;
    private final Translator translator;

    public InputButton(TradeSession session, PaperServices paperServices) {
        this.session = session;
        this.paperServices = paperServices;
        this.translator = this.paperServices.getTranslator();
    }

    private boolean canReceiveAfterTrade(Player player,
                                         List<ItemStack> toRemove,
                                         List<ItemStack> toAdd) {

        ItemStack[] contents = player.getInventory().getContents();

        int freedSlots = 0;
        for (ItemStack removeStack : toRemove) {
            if (removeStack == null) continue;

            int amountToRemove = removeStack.getAmount();
            for (ItemStack invStack : contents) {
                if (invStack == null || invStack.getType() == Material.AIR) continue;

                if (invStack.isSimilar(removeStack)) {
                    int removeFromStack = Math.min(invStack.getAmount(), amountToRemove);
                    amountToRemove -= removeFromStack;

                    if (removeFromStack == invStack.getAmount()) {
                        freedSlots++;
                    }

                    if (amountToRemove <= 0) break;
                }
            }
        }

        int emptySlots = 0;
        for (ItemStack stack : contents) {
            if (stack == null || stack.getType() == Material.AIR) {
                emptySlots++;
            }
        }

        ItemStack[] simulatedContents = new ItemStack[contents.length];
        for (int i = 0; i < contents.length; i++) {
            simulatedContents[i] = contents[i] == null ? null : contents[i].clone();
        }

        for (ItemStack removeStack : toRemove) {
            if (removeStack == null) continue;

            int amountToRemove = removeStack.getAmount();
            for (int i = 0; i < simulatedContents.length; i++) {
                ItemStack invStack = simulatedContents[i];
                if (invStack == null || invStack.getType() == Material.AIR) continue;

                if (invStack.isSimilar(removeStack)) {
                    int removeFromStack = Math.min(invStack.getAmount(), amountToRemove);
                    amountToRemove -= removeFromStack;

                    invStack.setAmount(invStack.getAmount() - removeFromStack);
                    if (invStack.getAmount() <= 0) {
                        simulatedContents[i] = null;
                    }

                    if (amountToRemove <= 0) break;
                }
            }
        }

        int invSize = player.getInventory().getSize();
        int roundedSize = (invSize / 9) * 9; // rounds down
        if (roundedSize > 54) roundedSize = 54;
        if (roundedSize < 9) roundedSize = 9;


        Inventory tempInv = Bukkit.createInventory(null, roundedSize);
        tempInv.setContents(simulatedContents);

        Map<Integer, ItemStack> leftovers = tempInv.addItem(toAdd.toArray(new ItemStack[0]));

        return leftovers.isEmpty();
    }


    @Override
    public ItemStack getDisplayItem(Player player) {
        UUID playerId = player.getUniqueId();
        List<ItemStack> myItems = session.getPlayerItems(playerId);
        List<ItemStack> otherItems = session.getOtherPlayerItems(playerId);
        boolean confirmed = session.hasConfirmed(playerId);
        int countdown = session.getCountdown();

        boolean fits = canReceiveAfterTrade(player, myItems, otherItems);
        boolean dropMode = PrismaCoreSetting.TRADE_DROP_ITEMS;
        boolean blockNoSpace = !dropMode && !fits;

        return createInputButtonItem(player, myItems, otherItems, confirmed, countdown, blockNoSpace);
    }

    private ItemStack createInputButtonItem(Player player,
                                            List<ItemStack> myItems,
                                            List<ItemStack> otherItems,
                                            boolean confirmed,
                                            int countdown,
                                            boolean blockNoSpace) {

        int amount = Math.max(1, countdown);
        Material material;
        String key;

        if (blockNoSpace) {
            key = "TRADE.MENU.INPUT_BUTTON.NO_SPACE";
            material = Material.BARRIER; // You can change to exact one if needed
        } else if (confirmed) {
            key = "TRADE.MENU.INPUT_BUTTON.CONFIRMED";
            material = Material.GREEN_TERRACOTTA;
        } else if (countdown > 1) {
            key = "TRADE.MENU.INPUT_BUTTON.COUNTDOWN";
            material = Material.RED_TERRACOTTA;
        } else if (myItems.isEmpty() && otherItems.isEmpty()) {
            key = "TRADE.MENU.INPUT_BUTTON.EMPTY";
            material = Material.GRAY_TERRACOTTA;
        } else if (myItems.isEmpty()) {
            key = "TRADE.MENU.INPUT_BUTTON.GIFT";
            material = Material.BLUE_TERRACOTTA;
        } else if (otherItems.isEmpty()) {
            key = "TRADE.MENU.INPUT_BUTTON.WARNING";
            material = Material.YELLOW_TERRACOTTA; // Optional visual warning material
        } else {
            key = "TRADE.MENU.INPUT_BUTTON.DEAL";
            material = Material.ORANGE_TERRACOTTA;
        }

        List<String> lore = translator.getListString(player, key + ".LORE", LocalPlaceholders.builder()
                .add("<countdown>", String.valueOf(countdown)));
        String displayName = translator.getAsText(player, key + ".DISPLAY_NAME", LocalPlaceholders.builder()
                .add("<countdown>", String.valueOf(countdown)));

        return new ItemBuilder(material)
                .setName(displayName)
                .setLore(lore)
                .setAmount(amount)
                .build();
    }


    @Override
    public void clicked(Player player) {
        UUID playerId      = player.getUniqueId();
        List<ItemStack> myItems    = session.getPlayerItems(playerId);
        List<ItemStack> otherItems = session.getOtherPlayerItems(playerId);

        boolean dropMode = PrismaCoreSetting.TRADE_DROP_ITEMS;
        boolean fits = canReceiveAfterTrade(player, myItems, otherItems);

        if (!dropMode && !fits) {
            translator.send(player, "TRADE.MESSAGES.INVENTORY_FULL");
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1F, 1F);
            return;
        }

        if (myItems.isEmpty() && otherItems.isEmpty()) {
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1F, 1F);
            return;
        }

        session.toggleConfirmation(playerId);
        this.paperServices.getTradeService().reopenMenus(session);

        if (session.bothConfirmed()) {
            this.paperServices.getTradeService().completeTrade(session);
        }
    }
}