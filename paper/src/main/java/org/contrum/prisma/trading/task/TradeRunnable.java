package org.contrum.prisma.trading.task;

import lombok.RequiredArgsConstructor;
import org.bukkit.scheduler.BukkitRunnable;
import org.contrum.prisma.trading.TradeService;
import org.contrum.prisma.trading.TradeSession;

import java.util.HashSet;
import java.util.Set;


@RequiredArgsConstructor
public class TradeRunnable extends BukkitRunnable {
    private final TradeService tradeService;

    @Override
    public void run() {
        Set<TradeSession> processedSessions = new HashSet<>();

        for (TradeSession session : tradeService.getActiveTrades().values()) {
            if (!processedSessions.add(session)) continue;

            int countdown = session.getCountdown();
            if (countdown > 1) {
                session.setCountdown(countdown - 1);

                //TODO
                // make async
                //Threads.sync(session::reopenMenus);
                tradeService.reopenMenus(session);
            }
        }
    }
}
