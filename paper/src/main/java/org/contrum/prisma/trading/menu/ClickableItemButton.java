package org.contrum.prisma.trading.menu;

import lombok.RequiredArgsConstructor;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.trading.TradeSession;
import org.contrum.tritosa.Translator;

import java.util.List;

@RequiredArgsConstructor
public class ClickableItemButton extends Button {
    private final TradeSession tradeSession;
    private final PaperServices paperServices;
    private final ItemStack item;
    private final int index;



    @Override
    public ItemStack getDisplayItem(Player player) {
        return item;
    }

    @Override
    public void clicked(Player player) {
        if (player == null) return;

        List<ItemStack> items = tradeSession.getPlayerItems(player.getUniqueId());
        if (index >= items.size()) return;

        if (player.getInventory().firstEmpty() != -1) {
            player.getInventory().addItem(items.get(index));
            player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1F, 1F);
            this.paperServices.getTradeService().removeItemFromTrade(player, index);
            this.paperServices.getTradeService().reopenMenus(tradeSession);

        } else {
            player.playSound(player.getLocation(), Sound.ENTITY_VILLAGER_NO, 1F, 1F);
            Translator translator = paperServices.getTranslator();
            translator.send(player, "TRADE.MESSAGES.INVENTORY_FULL");
        }
    }
}