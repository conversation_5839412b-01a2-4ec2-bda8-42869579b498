package org.contrum.prisma.trading;

import lombok.Getter;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.NamespacedKey;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.contrum.chorpu.chat.CC;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCoreSetting;
import org.contrum.prisma.events.CurrencyAddEvent;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.currency.Currency;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.trading.events.TradeCancelEvent;
import org.contrum.prisma.trading.events.TradeCompleteEvent;
import org.contrum.prisma.trading.events.TradeStartEvent;
import org.contrum.prisma.trading.listener.TradeListener;
import org.contrum.prisma.trading.menu.TradeMenu;
import org.contrum.prisma.trading.task.TradeRunnable;
import org.contrum.tritosa.Translator;
import org.bson.Document;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

public class TradeService implements Listener {



    private final PaperServices paperServices;
    private final Translator translator;

    @Getter
    private final Map<UUID, TradeSession> activeTrades = new ConcurrentHashMap<>();
    private final Map<UUID, Deque<UUID>> pendingRequests = new ConcurrentHashMap<>();
    @Getter
    private final Set<UUID> updatingMenus = ConcurrentHashMap.newKeySet();
    private final NamespacedKey coinKey;


    public TradeService(PaperServices paperServices) {
        this.paperServices = paperServices;
        this.translator = this.paperServices.getTranslator();
        new TradeListener(paperServices, this);
        new TradeRunnable(this).runTaskTimer(paperServices.getPlugin(), 0L, 22L);
        this.coinKey = new NamespacedKey(paperServices.getPlugin(), "trade_coin");
    }




    public TradeRequestResult sendTradeRequest(Player sender, Player target) {
        if (sender.equals(target)) return TradeRequestResult.SELF_REQUEST;

        Profile targetProfile = paperServices.getProfileService().getProfile(target.getUniqueId());
        if (targetProfile != null && targetProfile.isTradeIgnore()) {
            translator.send(sender, "TRADE.MESSAGES.TARGET_IGNORING");
            return TradeRequestResult.TARGET_IGNORING;
        }

        UUID senderId = sender.getUniqueId();
        UUID targetId = target.getUniqueId();

        Deque<UUID> senderRequests = pendingRequests.get(senderId);
        if (senderRequests != null && senderRequests.contains(targetId)) {
            senderRequests.remove(targetId);
            removeRequestFromQueue(targetId, senderId);
            TradeSession session = new TradeSession(senderId, targetId);
            activeTrades.put(senderId, session);
            activeTrades.put(targetId, session);
            new TradeMenu(session, paperServices).open(sender);
            new TradeMenu(session, paperServices).open(target);
            translator.send(sender, "TRADE.MESSAGES.REQUEST_ACCEPTED", LocalPlaceholders.builder()
                    .add("<player>", target.getName()));
            translator.send(target, "TRADE.MESSAGES.REQUEST_ACCEPTED_BY_TARGET", LocalPlaceholders.builder()
                    .add("<player>", sender.getName()));
            return TradeRequestResult.SUCCESS;
        }

        Deque<UUID> targetRequests = pendingRequests.get(targetId);
        if (targetRequests != null && targetRequests.contains(senderId)) {
            translator.send(sender, "TRADE.MESSAGES.REQUEST_FAILED");
            return TradeRequestResult.ALREADY_HAS_PENDING_REQUEST;
        }

        pendingRequests.computeIfAbsent(targetId, k -> new ArrayDeque<>()).addLast(senderId);
        translator.send(sender, "TRADE.MESSAGES.REQUEST_SENT", LocalPlaceholders.builder()
                .add("<target>", target.getName()));
        translator.send(target, "TRADE.MESSAGES.REQUEST_RECEIVED", LocalPlaceholders.builder()
                .add("<sender>", sender.getName()));
        return TradeRequestResult.SUCCESS;
    }

    public boolean acceptTradeRequest(Player target) {
        UUID targetId = target.getUniqueId();
        Deque<UUID> requests = pendingRequests.get(targetId);
        if (requests == null || requests.isEmpty()) {
            translator.send(target, "TRADE.MESSAGES.NO_PENDING_REQUEST");
            return false;
        }

        UUID senderId = requests.removeLast();
        if (requests.isEmpty()) pendingRequests.remove(targetId);

        Player sender = Bukkit.getPlayer(senderId);
        if (sender == null || !sender.isOnline()) return false;

        removeRequestFromAllQueues(senderId);

        TradeSession session = new TradeSession(senderId, targetId);
        activeTrades.put(senderId, session);
        activeTrades.put(targetId, session);

        new TradeMenu(session, paperServices).open(sender);
        new TradeMenu(session, paperServices).open(target);

        translator.send(target, "TRADE.MESSAGES.REQUEST_ACCEPTED_BY_TARGET", LocalPlaceholders.builder()
                .add("<player>", sender.getName()));
        translator.send(sender, "TRADE.MESSAGES.REQUEST_ACCEPTED", LocalPlaceholders.builder()
                .add("<player>", target.getName()));

        Bukkit.getPluginManager().callEvent(new TradeStartEvent(session));
        return true;
    }

    public boolean addItemToTrade(Player player, ItemStack item) {
        TradeSession session = getSession(player);
        if (session == null) return false;

        List<ItemStack> items = session.getPlayerItems(player.getUniqueId());
        if (items.size() >= TradeSession.MAX_ITEMS_PER_PLAYER) {
            translator.send(player, "TRADE.MESSAGES.ITEMS_FULL");
            return false;
        }

        items.add(item);
        resetTrade(session);
        return true;
    }

    public void removeItemFromTrade(Player player, int index) {
        TradeSession session = getSession(player);
        if (session == null) return;

        List<ItemStack> items = session.getPlayerItems(player.getUniqueId());
        if (index >= 0 && index < items.size()) {
            items.remove(index);
            resetTrade(session);
        }
    }

    public void toggleConfirmation(Player player) {
        TradeSession session = getSession(player);
        if (session == null || session.getCountdown() > 1) return;

        UUID id = player.getUniqueId();
        if (id.equals(session.getSender())) {
            session.setSenderConfirmed(!session.isSenderConfirmed());
        } else if (id.equals(session.getTarget())) {
            session.setTargetConfirmed(!session.isTargetConfirmed());
        }
    }



    public void completeTrade(TradeSession session) {
        Player senderPlayer = Bukkit.getPlayer(session.getSender());
        Player targetPlayer = Bukkit.getPlayer(session.getTarget());

        if (senderPlayer == null || targetPlayer == null) {
            cancelTrade(session);
            return;
        }

        boolean dropOnFull = isTradeDropItems();
        Map<Integer, ItemStack> leftoverSender = senderPlayer.getInventory().addItem(
                session.getTargetItems().toArray(new ItemStack[0]));
        Map<Integer, ItemStack> leftoverTarget = targetPlayer.getInventory().addItem(
                session.getSenderItems().toArray(new ItemStack[0]));

        if (!dropOnFull && (!leftoverSender.isEmpty() || !leftoverTarget.isEmpty())) {
            translator.send(senderPlayer, "TRADE.MESSAGES.FAILED_NO_SPACE");
            translator.send(targetPlayer, "TRADE.MESSAGES.FAILED_NO_SPACE");
            reopenMenus(session);
            return;
        }

        leftoverSender.values().forEach(item ->
                senderPlayer.getWorld().dropItemNaturally(senderPlayer.getLocation(), item));
        leftoverTarget.values().forEach(item ->
                targetPlayer.getWorld().dropItemNaturally(targetPlayer.getLocation(), item));

        senderPlayer.playSound(senderPlayer.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);
        targetPlayer.playSound(targetPlayer.getLocation(), Sound.ENTITY_PLAYER_LEVELUP, 1.0f, 1.0f);

        sendTradeMessage(senderPlayer, session.getTargetItems(), session.getSenderItems());
        sendTradeMessage(targetPlayer, session.getSenderItems(), session.getTargetItems());

        // Add coins from trade items to balances
        addCoinsFromTradeToBalances(session);

        session.setCompletedAt(System.currentTimeMillis());

        activeTrades.remove(session.getSender());
        activeTrades.remove(session.getTarget());
        senderPlayer.closeInventory();
        targetPlayer.closeInventory();

        Bukkit.getPluginManager().callEvent(new TradeCompleteEvent(session));
    }


    private void addCoinsFromTradeToBalances(TradeSession session) {
        ProfilePaperMetadata senderMetadata = paperServices.getProfileService().getServerMetadata(session.getSender(), ProfilePaperMetadata.class);
        long coinsToSender = sumCoins(session.getTargetItems());
        senderMetadata.depositCurrency(paperServices.getPlugin(), Bukkit.getPlayer(session.getSender()), Currency.COINS, CurrencyAddEvent.Cause.TRADE_COMPLETED, coinsToSender);

        ProfilePaperMetadata targetMetadata = paperServices.getProfileService().getServerMetadata(session.getSender(), ProfilePaperMetadata.class);
        long coinsToTarget = sumCoins(session.getSenderItems());
        targetMetadata.depositCurrency(paperServices.getPlugin(), Bukkit.getPlayer(session.getSender()), Currency.COINS, CurrencyAddEvent.Cause.TRADE_COMPLETED, coinsToTarget);
    }

    private long sumCoins(List<ItemStack> items) {
        long total = 0;
        for (ItemStack item : items) {
            if (item == null || !item.hasItemMeta()) continue;
            Long coinAmount = item.getItemMeta().getPersistentDataContainer().get(coinKey, PersistentDataType.LONG);
            if (coinAmount != null && coinAmount > 0) {
                total += coinAmount;
            }
        }
        return total;
    }


    public void cancelTrade(TradeSession session) {
        if (session.getCompletedAt() > 0L) return;

        Player sender = Bukkit.getPlayer(session.getSender());
        Player target = Bukkit.getPlayer(session.getTarget());

        refundCoins(session.getSenderItems(), session.getSender());

        refundCoins(session.getTargetItems(), session.getTarget());

        // Return normal items to sender
        if (sender != null) {
            for (ItemStack item : session.getSenderItems()) {
                if (item == null || item.getType().isAir()) continue;
                if (isCoinItem(item)) continue; // skip coin items, already refunded
                HashMap<Integer, ItemStack> leftovers = sender.getInventory().addItem(item);
                leftovers.values().forEach(leftover ->
                        sender.getWorld().dropItemNaturally(sender.getLocation(), leftover)
                );
            }
            translator.send(sender, "TRADE.MESSAGES.TRADE_CANCELLED");
        }

        // Return normal items to target
        if (target != null) {
            for (ItemStack item : session.getTargetItems()) {
                if (item == null || item.getType().isAir()) continue;
                if (isCoinItem(item)) continue; // skip coin items, already refunded
                HashMap<Integer, ItemStack> leftovers = target.getInventory().addItem(item);
                leftovers.values().forEach(leftover ->
                        target.getWorld().dropItemNaturally(target.getLocation(), leftover)
                );
            }
            translator.send(target, "TRADE.MESSAGES.TRADE_CANCELLED");
        }

        // Remove session from active trades
        activeTrades.remove(session.getSender());
        activeTrades.remove(session.getTarget());

        Bukkit.getPluginManager().callEvent(new TradeCancelEvent(session));
    }

    private boolean isCoinItem(ItemStack item) {
        if (item == null || !item.hasItemMeta()) return false;
        return item.getItemMeta().getPersistentDataContainer().has(coinKey, PersistentDataType.LONG);
    }

    private void refundCoins(List<ItemStack> items, UUID playerId) {
        long refundAmount = 0;
        for (ItemStack item : items) {
            if (item == null || !item.hasItemMeta()) continue;
            Long coins = item.getItemMeta().getPersistentDataContainer().get(coinKey, PersistentDataType.LONG);
            if (coins != null && coins > 0) {
                refundAmount += coins;
            }
        }
        if (refundAmount > 0) {
            ProfilePaperMetadata metadata = paperServices.getProfileService().getServerMetadata(playerId, ProfilePaperMetadata.class);
            metadata.depositCurrency(paperServices.getPlugin(), Bukkit.getPlayer(playerId), Currency.COINS, CurrencyAddEvent.Cause.TRADE_CANCELLED, refundAmount);
        }
    }



    public void declineTrade(Player player) {
        UUID id = player.getUniqueId();
        pendingRequests.remove(id);
        removeRequestFromAllQueues(id);
        TradeSession session = activeTrades.remove(id);
        if (session != null) {
            activeTrades.remove(session.getOther(id));
            cancelTrade(session);
        }
        translator.send(player, "TRADE.MESSAGES.REQUEST_DECLINED");
    }

    public void reopenMenus(TradeSession session) {
        Player sender = Bukkit.getPlayer(session.getSender());
        Player target = Bukkit.getPlayer(session.getTarget());

        if (sender != null) {
            updatingMenus.add(sender.getUniqueId());
            new TradeMenu(session, paperServices).open(sender);
        }

        if (target != null) {
            updatingMenus.add(target.getUniqueId());
            new TradeMenu(session, paperServices).open(target);
        }

        // Optional: Schedule delayed removal of flag
        Bukkit.getScheduler().runTaskLater(paperServices.getPlugin(), () -> {
            if (sender != null) updatingMenus.remove(sender.getUniqueId());
            if (target != null) updatingMenus.remove(target.getUniqueId());
        }, 2L); // Delay by 2 ticks to allow inventory reopen
    }


    public boolean hasPendingRequest(Player player) {
        Deque<UUID> queue = pendingRequests.get(player.getUniqueId());
        return queue != null && !queue.isEmpty();
    }

    public TradeSession getSession(Player player) {
        return activeTrades.get(player.getUniqueId());
    }

    private void resetTrade(TradeSession session) {
        session.setCountdown(4);
        session.setSenderConfirmed(false);
        session.setTargetConfirmed(false);
    }

    private void removeRequestFromQueue(UUID target, UUID sender) {
        Deque<UUID> queue = pendingRequests.get(target);
        if (queue != null) {
            queue.remove(sender);
            if (queue.isEmpty()) pendingRequests.remove(target);
        }
    }

    private void removeRequestFromAllQueues(UUID id) {
        pendingRequests.values().forEach(queue -> queue.remove(id));
        pendingRequests.entrySet().removeIf(entry -> entry.getValue().isEmpty());
    }

    public boolean isTradeDropItems() {
        return PrismaCoreSetting.TRADE_DROP_ITEMS;
    }

    private void sendTradeMessage(Player player, List<ItemStack> received, List<ItemStack> given) {
        translator.send(player, "TRADE.MESSAGES.TRADE_COMPLETED");

        for (ItemStack item : given) {
            if (item != null && item.getType() != org.bukkit.Material.AIR) {
                translator.send(player, "TRADE.MESSAGES.ITEM_GIVEN_FORMAT",
                        LocalPlaceholders.builder()
                                .add("<item>", getItemDisplayName(item))
                                .add("<amount>", String.valueOf(item.getAmount())));
            }
        }

        for (ItemStack item : received) {
            if (item != null && item.getType() != org.bukkit.Material.AIR) {
                translator.send(player, "TRADE.MESSAGES.ITEM_RECEIVED_FORMAT",
                        LocalPlaceholders.builder()
                                .add("<item>", getItemDisplayName(item))
                                .add("<amount>", String.valueOf(item.getAmount())));
            }
        }
    }


    private String getItemDisplayName(ItemStack item) {
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return item.getItemMeta().getDisplayName();
        }
        return Arrays.stream(item.getType().name().toLowerCase().split("_"))
                .map(word -> Character.toUpperCase(word.charAt(0)) + word.substring(1))
                .collect(Collectors.joining(" "));
    }

    public void addCoinToTrade(Player player, TradeSession session, long amount) {
        if (session == null) {
            translator.send(player, "TRADE.MESSAGES.NO_ACTIVE_TRADE");
            return;
        }

        // Check if player has enough coins before deducting
        ProfilePaperMetadata metadata = paperServices.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        long playerBalance = metadata.getBalance(Currency.COINS);
        if (playerBalance < amount) {
            translator.send(player, "TRADE.MESSAGES.COIN_NOT_ENOUGH");
            return;
        }

        List<ItemStack> playerItems = session.getPlayerItems(player.getUniqueId());
        if (playerItems.size() >= TradeSession.MAX_ITEMS_PER_PLAYER) {
            translator.send(player, "TRADE.MESSAGES.ITEMS_FULL");
            return;
        }

        // Deduct coins immediately from player's balance
        metadata.withdrawCurrency(Currency.COINS, amount);

        // Create a generic item to represent coins (using PAPER here)
        ItemStack coinItem = new ItemStack(Material.PAPER, 1); // Always 1 item to avoid stacking issues
        ItemMeta meta = coinItem.getItemMeta();

        meta.setDisplayName(translator.getAsText(player, "TRADE.ITEMS.COIN.NAME"));
        meta.setLore(translator.getListString(player, "TRADE.ITEMS.COIN.LORE",
                LocalPlaceholders.builder()
                        .add("<amount>", String.valueOf(amount))
        ));

        // Store the coin amount in persistent data container
        meta.getPersistentDataContainer().set(coinKey, PersistentDataType.LONG, amount);
        coinItem.setItemMeta(meta);

        playerItems.add(coinItem);

        resetTrade(session);

        translator.send(player, "TRADE.MESSAGES.COIN_ADDED",
                LocalPlaceholders.builder()
                        .add("<amount>", String.valueOf(amount)));
    }




}