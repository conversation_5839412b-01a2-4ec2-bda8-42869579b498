package org.contrum.prisma.trading.listener;

import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityPickupItemEvent;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.trading.TradeService;
import org.contrum.prisma.trading.TradeSession;
import org.contrum.tritosa.Translator;

public class TradeListener implements Listener {

    private final PaperServices paperServices;
    private final TradeService tradeService;
    private final Translator translator;

    public TradeListener(PaperServices paperServices, TradeService tradeService) {
        this.paperServices = paperServices;
        this.tradeService = tradeService;
        this.translator = this.paperServices.getTranslator();
        Bukkit.getPluginManager().registerEvents(this, paperServices.getPlugin());
    }


    @EventHandler
    public void onPlayerInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player player)) return;
        if (event.getSlot() == event.getRawSlot()) return;

        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType().isAir()) return;

        TradeSession session = tradeService.getSession(player);
        if (session == null || session.isCompleted()) return;

        event.setCancelled(true);

        boolean added = tradeService.addItemToTrade(player, clickedItem.clone());
        if (!added) {
            translator.send(player, "TRADE.MESSAGES.ITEMS_FULL");
            return;
        }

        player.playSound(player.getLocation(), Sound.ENTITY_ITEM_PICKUP, 1.0f, 1.0f);
        player.getInventory().setItem(event.getSlot(), null);

        tradeService.reopenMenus(session);
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player quitter = event.getPlayer();
        TradeSession session = tradeService.getSession(quitter);

        if (session != null && !session.isCompleted()) {
            tradeService.cancelTrade(session);
            Player partner = Bukkit.getPlayer(session.getOther(quitter.getUniqueId()));
            if (partner != null && partner.isOnline()) {
                partner.closeInventory();
            }
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player player)) return;

        // If we're updating, ignore this close event
        if (tradeService.getUpdatingMenus().remove(player.getUniqueId())) return;

        TradeSession session = tradeService.getSession(player);
        if (session == null || session.isCompleted()) return;
        if (session.bothConfirmed()) return;

        tradeService.cancelTrade(session);

        Player partner = Bukkit.getPlayer(session.getOther(player.getUniqueId()));
        if (partner != null && partner.isOnline()) {
            partner.closeInventory();
        }
    }


    @EventHandler(priority = EventPriority.HIGH, ignoreCancelled = true)
    public void onEntityPickup(EntityPickupItemEvent event) {
        if (!(event.getEntity() instanceof Player player)) return;
        TradeSession session = tradeService.getSession(player);
        if (session == null || session.isCompleted()) return;
        event.setCancelled(true);
    }

}