package org.contrum.prisma.trading.menu;

import org.bukkit.Material;
import org.bukkit.conversations.ConversationAbandonedEvent;
import org.bukkit.conversations.ConversationContext;
import org.bukkit.conversations.Prompt;
import org.bukkit.conversations.StringPrompt;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.ChatUtils;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.trading.TradeService;
import org.contrum.prisma.trading.TradeSession;
import org.contrum.tritosa.Translator;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

public class CoinTradeButton extends Button {

    private final TradeSession session;
    private final PaperServices paperServices;
    private final TradeService tradeService;
    private final Translator translator;

    public CoinTradeButton(TradeSession session, PaperServices paperServices, TradeService tradeService) {
        this.session = session;
        this.paperServices = paperServices;
        this.tradeService = tradeService;
        this.translator = paperServices.getTranslator();
    }

    @Override
    public ItemStack getDisplayItem(Player viewer) {
        return new ItemBuilder(Material.GOLD_INGOT)
                .setName(translator.getAsText(viewer, "TRADE.MENU.COIN_TRADE_BUTTON.DISPLAY_NAME"))
                .setLore(
                        translator.getListString(viewer, "TRADE.MENU.COIN_TRADE_BUTTON.LORE")
                )
                .build();
    }

    @Override
    public void clicked(Player player) {
        tradeService.getUpdatingMenus().add(player.getUniqueId()); // Mark updating

        ChatUtils.beginCancellablePrompt(player, new StringPrompt() {
            @NotNull
            @Override
            public String getPromptText(@NotNull ConversationContext context) {
                return translator.getAsText(player, "TRADE.MESSAGES.COIN_INPUT_PROMPT");
            }

            @Override
            public @Nullable Prompt acceptInput(@NotNull ConversationContext context, @Nullable String input) {
                try {
                    if (input == null || input.equalsIgnoreCase("cancel")) {
                        translator.send(player, "TRADE.MESSAGES.COIN_INPUT_CANCELLED");
                        return Prompt.END_OF_CONVERSATION;
                    }

                    int amount = Integer.parseInt(input);

                    if (amount <= 0) {
                        translator.send(player, "TRADE.MESSAGES.COIN_INPUT_INVALID_AMOUNT");
                        return this;
                    }

                    tradeService.addCoinToTrade(player, session, amount);
                    translator.send(player, "TRADE.MESSAGES.COIN_INPUT_SUCCESS", amount);
                    player.playSound(player.getLocation(), "ui.button.click", 1, 1);
                    tradeService.getUpdatingMenus().remove(player.getUniqueId());
                    return Prompt.END_OF_CONVERSATION;
                } catch (NumberFormatException e) {
                    translator.send(player, "TRADE.MESSAGES.COIN_INPUT_NOT_NUMBER");
                    return this;
                }
            }


        }, paperServices.getPlugin());
    }

}