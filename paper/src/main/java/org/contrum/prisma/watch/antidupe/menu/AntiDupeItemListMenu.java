package org.contrum.prisma.watch.antidupe.menu;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.storage.StorageMenu;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.menus.CorePaginatedMenu;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.prisma.watch.antidupe.item.DupedItem;
import org.contrum.prisma.watch.antidupe.item.WADItem;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

public class AntiDupeItemListMenu extends CorePaginatedMenu {

    private final List<String> ids;
    private final List<Button> buttons;
    private final boolean allowRemove;
    private final boolean showDuped; // Show item data from dupe logs

    public AntiDupeItemListMenu(PaperServices services, List<WADItem> items, boolean allowRemove, boolean showDuped) {
        super(services);

        this.allowRemove = allowRemove;
        this.showDuped = showDuped;

        List<WADItem> limit = items.stream().limit(1000).toList();
        this.ids = limit.stream().map(WADItem::getID).toList();
        if (!showDuped) {
            // create items once
            this.buttons = new ArrayList<>();
            for (WADItem wadItem : limit) {
                ItemBuilder1_20 builder = new ItemBuilder1_20(wadItem.getType());
                builder.name("&e" + wadItem.getID());
                builder.addLore("&f");
                builder.addLore("&eID: &c" + wadItem.getID());
                builder.addLore("&eOwner: &c" + wadItem.getCurrentOwner().getName());
                builder.addLore("&eCreationDate: &c" + TimeUtils.toDate(wadItem.getCreationDate()));
                builder.addLore("&eLastUpdate: &c" + TimeUtils.toDate(wadItem.getLastInteractionDate()));
                WADItem.ItemLocation location = wadItem.getLocation();
                if (location != null) {
                    builder.addLore("&f");
                    builder.addLore("&eLocation: &c" + location.getInventoryType().name());
                    builder.addLore("&eSlot: &c" + location.getSlot());
                    if (location.isShulker()) {
                        builder.addLore("&eIsInShulker: &atrue");
                        builder.addLore("&eShulker Slot: &c" + location.getShulkerSlot());
                    }
                }
                builder.addLore("&f");
                builder.addLore("&eDuped: &c" + wadItem.isDupeMarked());
                builder.addLore("&eLogs: &c" + wadItem.getLogs().size());

                buttons.add(Button.of(builder.build(), (clicker, type) -> {
                    new AntiDupeItemInfoMenu(getServices(), wadItem, new ItemStack(wadItem.getType()))
                            .setBack(this::open).open(clicker);
                }));
            }
        } else {
            this.buttons = new ArrayList<>();
            for (WADItem wadItem : limit) {
                for (DupedItem dupedItem : wadItem.getDupedCopies()) {
                    ItemBuilder1_20 builder = new ItemBuilder1_20(wadItem.getType());
                    builder.name("&e" + wadItem.getID());
                    builder.addLore("&f");
                    builder.addLore("&eID: &c" + wadItem.getID());
                    builder.addLore("&eOwner: &c" + dupedItem.getCurrentOwner().getName());
                    builder.addLore("&eCreationDate: &c" + TimeUtils.toDate(dupedItem.getDupeDate()));
                    WADItem.ItemLocation location = dupedItem.getLocation();
                    if (location != null) {
                        builder.addLore("&f");
                        builder.addLore("&eLocation: &c" + location.getInventoryType().name());
                        builder.addLore("&eSlot: &c" + location.getSlot());
                        if (location.isShulker()) {
                            builder.addLore("&eIsInShulker: &atrue");
                            builder.addLore("&eShulker Slot: &c" + location.getShulkerSlot());
                        }
                    }
                    builder.addLore("&f");
                    builder.addLore("&eDuped: &ctrue");
                    builder.addLore("&eLogs: &c" + wadItem.getLogs().size());

                    buttons.add(Button.of(builder.build(), (clicker, type) -> {
                        new AntiDupeItemInfoMenu(getServices(), wadItem, new ItemStack(dupedItem.getItem().getMaterial()))
                                .setBack(this::open).open(clicker);
                    }));
                }
            }
        }
    }

    @Override
    public List<Button> getPaginatedButtons(Player player) {
        return buttons;
    }

    @Override
    public Map<Integer, Button> getGlobalButtons(Player player) {
        Map<Integer, Button> buttons = new HashMap<>();

        // fill last row
        for (int i = 37; i < 45; i++) {
            buttons.put(i, Button.of(this.getFillItem()));
        }

        if (allowRemove) {
            ItemBuilder1_20 removeBuilder = new ItemBuilder1_20(Material.REDSTONE_BLOCK);
            removeBuilder.name("&cDestroy Items");
            removeBuilder.addLore("&7");
            removeBuilder.addLore("&7This will remove all items in the list");
            removeBuilder.addLore("&7From players inventories");
            buttons.put(48, Button.of(removeBuilder.build(), (clicker) -> {
                player.sendMessage(CC.translate("&cRemoving items, this may take a while..."));
                CompletableFuture<List<ItemStack>> future;
                clicker.closeInventory();
                if (showDuped) {
                    future  = getServices().getPrismaWatchService().getAntiDupeService().getItemManager().findDupedItemsForIds(ids, (item) -> {
                        for (ItemStack itemStack : item) {
                            itemStack.setAmount(0);
                        }
                        return true;
                    });
                } else {
                    future = getServices().getPrismaWatchService().getAntiDupeService().getItemManager().findItems(ids, (item) -> {
                        item.setAmount(0);
                        return true;
                    });
                }

                future.whenComplete((items, err) -> {
                    if (err != null) {
                        player.sendMessage(CC.translate("&cAn error occurred while removing items: " + err.getMessage()));
                        err.printStackTrace();
                    } else {
                        player.sendMessage(CC.translate("&aSuccessfully removed " + items.size() + " items from players inventories."));
                    }
                });
            }));
            ItemBuilder1_20 resetBuilder = new ItemBuilder1_20(Material.GOLD_BLOCK);
            resetBuilder.name("&eReset Items");
            resetBuilder.addLore("&7");
            resetBuilder.addLore("&7This remove dupe flags and change IDs of all items in the list");
            resetBuilder.addLore("&7From players inventories");
            buttons.put(50, Button.of(resetBuilder.build(), (clicker) -> {
                player.sendMessage(CC.translate("&cResetting items"));
                CompletableFuture<List<ItemStack>> future;
                clicker.closeInventory();
                if (showDuped) {
                    future  = getServices().getPrismaWatchService().getAntiDupeService().getItemManager().findDupedItemsForIds(ids, (item) -> {
                        for (ItemStack itemStack : item) {
                            getServices().getPrismaWatchService().getAntiDupeService().getItemManager().resetItem(itemStack);
                        }
                        return true;
                    });
                } else {
                    future = getServices().getPrismaWatchService().getAntiDupeService().getItemManager().findItems(ids, (item) -> {
                        getServices().getPrismaWatchService().getAntiDupeService().getItemManager().resetItem(item);
                        return true;
                    });
                }

                future.whenComplete((items, err) -> {
                    if (err != null) {
                        player.sendMessage(CC.translate("&cAn error occurred while removing items: " + err.getMessage()));
                        err.printStackTrace();
                    } else {
                        player.sendMessage(CC.translate("&aSuccessfully reset " + items.size() + " items from players inventories."));
                    }
                });
            }));
        }

        return buttons;
    }

    @Override
    public int getRows(Player player) {
        return 6;
    }

    @Override
    public boolean isAutoUpdate() {
        return false;
    }

    @Override
    public StorageMenu.FillType getFillType() {
        return StorageMenu.FillType.ONLY_CORNERS;
    }

    @Override
    public String getTitle(Player player) {
        return "Item Search";
    }
}
