package org.contrum.prisma.watch.antidupe.processor;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.block.ShulkerBox;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.BlockStateMeta;
import org.contrum.prisma.utils.PlayerVaultsUtils;
import org.contrum.prisma.watch.antidupe.item.DupedItem;
import org.contrum.prisma.watch.antidupe.item.WADItem;
import org.contrum.prisma.watch.antidupe.item.WADItemManager;
import org.contrum.prisma.watch.antidupe.item.log.WADItemLogDupeMarked;
import org.contrum.prisma.watch.antidupe.processor.utils.GlobalItemTracker;
import org.contrum.prisma.watch.antidupe.processor.utils.ProcessorSettings;
import org.jetbrains.annotations.Nullable;

import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

@Getter
public class InventoryProcessor {

    private final WADItemManager itemManager;
    private final OfflinePlayer owner;
    private final Inventory inventory;
    private final ProcessorSettings settings;

    private final GlobalItemTracker.InventoryContext inventoryContext;

    // Replace parent reference with global tracker
    private final GlobalItemTracker globalTracker;

    // Results only store counts, not actual references to items
    private final ProcessResults results = new ProcessResults();

    private final List<PendingOperation> syncOperations = new ArrayList<>();
    private final Map<Integer, PendingShulkerOperation> pendingShulkerOperations = new HashMap<>();
    private boolean asyncProcess = false;

    public InventoryProcessor(WADItemManager itemManager, OfflinePlayer owner,
                              Inventory inventory, GlobalItemTracker globalTracker, ProcessorSettings settings, GlobalItemTracker.InventoryContext type) {
        this.itemManager = itemManager;
        this.owner = owner;
        this.inventory = inventory;
        this.globalTracker = globalTracker;
        this.settings = settings;
        this.inventoryContext = type;
    }

    /**
     * Extracts item from shulker box at specific slot
     */
    private ItemStack extractItemFromShulker(ItemStack shulkerItem, int shulkerSlot) {
        if (!isShulkerBox(shulkerItem)) return null;

        BlockStateMeta meta = (BlockStateMeta) shulkerItem.getItemMeta();
        if (meta != null && meta.getBlockState() instanceof ShulkerBox shulkerBox) {
            ItemStack[] shulkerContents = shulkerBox.getInventory().getContents();
            return shulkerSlot < shulkerContents.length ? shulkerContents[shulkerSlot] : null;
        }
        return null;
    }

    /**
     * Main processing method - handles entire inventory scan with optimized operations
     */
    public void process() {
        long start = System.currentTimeMillis();
        this.asyncProcess = this.canProcessAsync();

        if (!canProcess())
            return;

        int validItems = 0;

        long slotScanStart = System.currentTimeMillis();
        for (int slot = 0; slot < inventory.getSize(); slot++) {
            if (!canProcess()) // Cancel processing
                return;

            ItemStack item = inventory.getItem(slot);
            if (!isValidItem(item)) continue;
            validItems++;
            processItemAtSlot(slot, item);
        }
        long slotScanMs = System.currentTimeMillis() - slotScanStart;

        long pendingStart = System.currentTimeMillis();
        executeOptimizedPendingOperations();
        long pendingMs = System.currentTimeMillis() - pendingStart;

        long total = System.currentTimeMillis() - start;
        if (total > 100) {
            System.out.println("[AntiDupe] InventoryProcessor: " +
                    "Processed " + validItems + " items, " +
                    syncOperations.size() + " ops in " + total + "ms " +
                    "(scan: " + slotScanMs + "ms, pending: " + pendingMs + "ms)");
        }
    }

    private void validateProcessingState() {
        if (Bukkit.isPrimaryThread()) {
            throw new IllegalStateException("Processing must not run on main thread");
        }
    }

    /**
     * Checks if item is valid for processing
     */
    private boolean isValidItem(ItemStack item) {
        return item != null && itemManager.isCompatibleItem(item);
    }

    private void processItemAtSlot(int slot, ItemStack item) {
        ItemContext context = new ItemContext(slot, item, false, -1);
        processItemInContext(context);

        // Process shulker box contents if applicable
        if (isShulkerBox(item) && canProcessShulker(slot)) {
            processShulkerContents(slot, item);
        }
    }

    /**
     * Processes contents of a shulker box
     */
    private void processShulkerContents(int shulkerSlot, ItemStack shulkerItem) {
        BlockStateMeta meta = (BlockStateMeta) shulkerItem.getItemMeta();
        if (meta == null || !(meta.getBlockState() instanceof ShulkerBox shulkerBox)) return;

        Inventory shulkerInventory = shulkerBox.getInventory();
        results.processedShulkersCount++;

        for (int slot = 0; slot < shulkerInventory.getSize(); slot++) {
            ItemStack item = shulkerInventory.getItem(slot);
            if (!isValidItem(item)) continue;

            ItemContext context = new ItemContext(shulkerSlot, item, true, slot);
            processItemInContext(context);
        }
    }

    /**
     * Core item processing logic - unified for both regular items and shulker contents
     */
    private String processItemInContext(ItemContext context) {
        String itemId = itemManager.getId(context.item);
        results.processedItemsCount++;

        // Handle items without IDs
        if (itemId == null) {
            results.idsAssignedCount++;
            handleOperation(context, null, this::ensureItemId);
            return null;
        }

        // Skip already duped items
        if (itemManager.isDuped(context.item)) {
            return null;
        }

        // Check for duplicate using global tracker - crucial memory optimization here
        GlobalItemTracker.ContainerType itemType = context.isShulkerItem ? GlobalItemTracker.ContainerType.SHULKER_BOX : inventoryContext.getContainerType();

        boolean isNewItem = globalTracker.registerItemId(itemId, owner.getUniqueId(),
                owner.getName(), itemType);

        if (!isNewItem) {
            // This is a duplicate
            results.dupedItemsCount++;

            handleOperation(context, itemId, (i) -> markItemAsDuped(context.createLocation(inventoryContext), i));
            return null;
        }

        // Update item data if requested
        if (settings.isUpdateItemOwners()) {
            itemManager.updateItemData(owner, context.item, context.createLocation(inventoryContext));

            // Check for removed duplicates
            itemManager.findDupedItemsWithNull(itemId, i -> false)
                    .whenComplete((list, err) -> {
                        if (err != null) {
                            System.err.println("Error checking for removed duplicates: " + err.getMessage());
                            err.printStackTrace();
                            return;
                        }

                        if (list == null || list.isEmpty()) return;

                        itemManager.getWADItem(itemId).thenAccept(wadItem -> {
                            if (wadItem == null) return;
                            List<DupedItem> dupedCopies = wadItem.getDupedCopies();
                            if (dupedCopies.isEmpty()) return;

                            boolean update = false;
                            for (int i = list.size() - 1; i >= 0; i--) {
                                if (list.get(i) == null && dupedCopies.size() > i) {
                                    dupedCopies.remove(i);
                                    update = true;
                                }
                            }

                            if (update) itemManager.saveWADItem(wadItem);
                        });
                    });
        }

        return itemId;
    }

    /**
     * Creates appropriate pending operation based on context
     */
    private void handleOperation(ItemContext context, @Nullable String itemId, Consumer<ItemStack> operation) {

        if (context.isShulkerItem) {
            PendingShulkerOperation shulkerOp = this.pendingShulkerOperations.computeIfAbsent(context.slot, c -> new PendingShulkerOperation(itemId, context.slot));
            shulkerOp.addOperation(context.shulkerSlot, context.item, operation);

            if (asyncProcess) {
                shulkerOp.tryExecute(this);
            }
        }

        if (asyncProcess) {
            operation.accept(context.item);
        } else {
            syncOperations.add(new PendingOperation(itemId, context.slot, context.item, operation));
        }
    }

    /**
     * Operation callbacks
     */
    private void ensureItemId(ItemStack item) {
        itemManager.ensureId(owner, item);
    }

    private void markItemAsDuped(WADItem.ItemLocation location, ItemStack item) {
        itemManager.markAsDuped(owner, item, location, WADItemLogDupeMarked.Reason.PROCESS_TASK);
    }

    private void executeOptimizedPendingOperations() {
        if (syncOperations.isEmpty() && pendingShulkerOperations.isEmpty()) {
            return;
        }

        syncOperations.addAll(pendingShulkerOperations.values());
        pendingShulkerOperations.clear();

        // Calculate optimal batch size based on operation count
        int optimalBatchSize = calculateOptimalBatchSize(syncOperations.size());
        List<List<PendingOperation>> batches = createBatches(syncOperations, optimalBatchSize);

        // Use CountDownLatch for lightweight synchronization
        CountDownLatch completionLatch = new CountDownLatch(batches.size());
        AtomicInteger completedBatches = new AtomicInteger(0);

        // Schedule all batches immediately without blocking
        for (int batchIndex = 0; batchIndex < batches.size(); batchIndex++) {
            final int currentBatchIndex = batchIndex;
            List<PendingOperation> batch = batches.get(batchIndex);

            // Schedule batch on main thread immediately - no synchronous waiting
            Bukkit.getScheduler().runTask(itemManager.getServices().getPlugin(), () -> {
                try {
                    // Execute all operations in this batch
                    for (PendingOperation operation : batch) {
                        try {
                            operation.tryExecute(this);
                        } catch (Exception e) {
                            System.err.println("Operation failed in batch " + (currentBatchIndex + 1) + ": " + e.getMessage());
                            e.printStackTrace();
                        }
                    }
                } finally {
                    completionLatch.countDown();
                }
            });
        }

        // Wait for completion with timeout to prevent hanging
        try {
            boolean completed = completionLatch.await(30, TimeUnit.SECONDS);
            if (!completed) {
                System.err.println("WARNING: Operations timed out after 30 seconds! Completed batches: " +
                        completedBatches.get() + "/" + batches.size());
            }
            syncOperations.clear();
        } catch (InterruptedException e) {
            System.err.println("ERROR: Operation execution was interrupted: " + e.getMessage());
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Calculate optimal batch size based on operation count for best performance
     */
    private int calculateOptimalBatchSize(int totalOperations) {
        if (totalOperations <= 10) return totalOperations;
        if (totalOperations <= 50) return 10;
        if (totalOperations <= 200) return 20;
        return 25;
    }

    /**
     * Create batches of operations for parallel processing
     */
    private List<List<PendingOperation>> createBatches(List<PendingOperation> operations, int batchSize) {
        List<List<PendingOperation>> batches = new ArrayList<>();
        for (int i = 0; i < operations.size(); i += batchSize) {
            int end = Math.min(i + batchSize, operations.size());
            batches.add(new ArrayList<>(operations.subList(i, end)));
        }
        return batches;
    }

    /**
     * Checks if item is a shulker box
     */
    private boolean isShulkerBox(ItemStack item) {
        return item.getType().name().contains("SHULKER_BOX");
    }

    /**
     * Attempts to process an operation synchronously
     */
    private boolean processSyncOperation(int slot, ItemStack originalItem, Consumer<ItemStack> operation) {
        ItemStack[] contents = inventory.getContents();
        if (slot >= contents.length) return false;

        ItemStack currentItem = contents[slot];

        if (!itemsMatch(originalItem, currentItem)) {
            return false;
        }

        operation.accept(currentItem);
        return true;
    }

    /**
     * Checks if two items match for sync operation validation
     */
    private boolean itemsMatch(ItemStack original, ItemStack current) {
        if (current == null || original == null) return false;

        return current.getType() == original.getType() &&
                current.getAmount() == original.getAmount() &&
                Objects.equals(current.getDisplayName(), original.getDisplayName());
    }

    private boolean canProcessShulker(int slot) {
        if (!owner.isOnline() || inventoryContext.getContainerType() != GlobalItemTracker.ContainerType.MAIN_INVENTORY)
            return true;

        Player player = owner.getPlayer();
        int heldItemSlot = player.getInventory().getHeldItemSlot();
        if (heldItemSlot == slot)
            return false;

        return true;
    }

    private boolean canProcess() {
        if (this.canProcessAsync() != this.asyncProcess) {
            return false;
        }

        if (!owner.isOnline()) {
            return true;
        }

        Player player = owner.getPlayer();
        if (player == null)
            return true;

        return switch (inventoryContext.getContainerType()) {
            case MAIN_INVENTORY, SHULKER_BOX:
                yield true;
            case ENDERCHEST:
                InventoryType type = player.getOpenInventory().getType();
                yield type != InventoryType.ENDER_CHEST;
            case VAULT:
                int openVault = PlayerVaultsUtils.getOpenVault(player.getUniqueId());
                yield openVault != inventoryContext.getVaultNumber();
        };
    }

    private boolean canProcessAsync() {
        if (!owner.isOnline()) {
            return true;
        }

        Player player = owner.getPlayer();
        if (player == null)
            return true;

        return switch (inventoryContext.getContainerType()) {
            case MAIN_INVENTORY, SHULKER_BOX:
                yield false;
            case ENDERCHEST, VAULT:
                yield true;
        };
    }

    /**
     * Context information for item processing
     */
    @AllArgsConstructor
    private static class ItemContext {
        final int slot;
        final ItemStack item;
        final boolean isShulkerItem;
        final int shulkerSlot;

        public WADItem.ItemLocation createLocation(GlobalItemTracker.InventoryContext type) {

            WADItem.InventoryType inventoryType = switch (type.getContainerType()) {
                case MAIN_INVENTORY, SHULKER_BOX -> WADItem.InventoryType.MAIN;
                case ENDERCHEST -> WADItem.InventoryType.ENDER_CHEST;
                case VAULT -> WADItem.InventoryType.VAULT;
            };

            if (inventoryType == WADItem.InventoryType.VAULT) {
                return new WADItem.ItemLocation(slot, inventoryType, isShulkerItem, shulkerSlot, type.getVaultNumber());
            }
            return new WADItem.ItemLocation(slot, inventoryType, isShulkerItem, shulkerSlot, -1);
        }
    }

    @Getter
    @NoArgsConstructor
    public static class ProcessResults {
        public int processedItemsCount;
        public int processedShulkersCount;
        public int dupedItemsCount;
        public int idsAssignedCount;

        public boolean needsSave() {
            return idsAssignedCount > 0 || dupedItemsCount > 0;
        }

        public void sum(ProcessResults results) {
            this.processedItemsCount += results.processedItemsCount;
            this.processedShulkersCount += results.processedShulkersCount;
            this.dupedItemsCount += results.dupedItemsCount;
            this.idsAssignedCount += results.idsAssignedCount;
        }

        public static ProcessResults of(ProcessResults... results) {
            ProcessResults combined = new ProcessResults();
            for (ProcessResults result : results) {
                combined.sum(result);
            }
            return combined;
        }
    }

    /**
     * Optimized lightweight PendingOperation for main thread execution
     */
    @AllArgsConstructor
    class PendingOperation {
        @Nullable
        protected final String itemId;
        protected final int slot;
        protected final ItemStack originalItem;
        protected final Consumer<ItemStack> operation;

        /**
         * Attempts to execute the operation
         */
        public boolean tryExecute(InventoryProcessor processor) {
            // Try original slot first
            if (processor.processSyncOperation(slot, originalItem, operation)) {
                return true;
            }

            // Search other slots if item moved
            if (itemId != null) {
                return searchAndExecute(processor);
            }

            return false;
        }

        /**
         * Searches for item in other slots and executes operation
         */
        protected boolean searchAndExecute(InventoryProcessor processor) {
            ItemStack[] contents = processor.getInventory().getContents();

            for (int i = 0; i < contents.length; i++) {
                if (contents[i] != null && processor.processSyncOperation(i, originalItem, operation)) {
                    return true;
                }
            }
            return false;
        }
    }

    /**
     * Specialized operation for shulker box items
     */
    final class PendingShulkerOperation extends PendingOperation {
        private final int shulkerSlot;
        private final ItemStack originalShulkerItem;
        private final Map<Integer, ItemData> operations = new HashMap<>();

        public PendingShulkerOperation(String itemId, int shulkerSlot) {
            super(itemId, -1, null, null);
            this.shulkerSlot = shulkerSlot;
            this.originalShulkerItem = inventory.getItem(shulkerSlot);
        }

        @AllArgsConstructor @Getter
        private class ItemData {
            final ItemStack original;
            final Consumer<ItemStack> consumer;
        }

        public void addOperation(int slot, ItemStack originalItem, Consumer<ItemStack> operation) {
            operations.put(slot, new ItemData(originalItem, operation));
        }

        @Override
        public boolean tryExecute(InventoryProcessor processor) {
            ItemStack[] contents = processor.getInventory().getContents();
            if (shulkerSlot >= contents.length) {
                this.operations.clear();
                return false;
            }

            ItemStack currentShulker = contents[shulkerSlot];

            if (!itemsMatch(originalShulkerItem, currentShulker) || !isShulkerBox(currentShulker)) {
                this.operations.clear();
                return false;
            }

            return processShulkerItem(currentShulker);
        }

        /**
         * Processes item within shulker box
         */
        private boolean processShulkerItem(ItemStack shulkerItem) {
            BlockStateMeta meta = (BlockStateMeta) shulkerItem.getItemMeta();
            if (meta == null || !(meta.getBlockState() instanceof ShulkerBox shulkerBox)) {
                this.operations.clear();
                return false;
            }

            boolean updated = false;
            ItemStack[] shulkerContents = shulkerBox.getInventory().getContents();
            for (Map.Entry<Integer, ItemData> entry : this.operations.entrySet()) {
                Integer s = entry.getKey();
                ItemData data = entry.getValue();

                if (s < 0 || s >= shulkerContents.length) {
                    continue; // Skip invalid slots
                }

                ItemStack targetItem = shulkerContents[s];

                if (!itemsMatch(data.getOriginal(), targetItem)) {
                    continue;
                }

                // Execute operation and update shulker
                updated = true;
                data.getConsumer().accept(targetItem);
            }

            if (updated) {
                meta.setBlockState(shulkerBox);
                shulkerItem.setItemMeta(meta);
            }
            this.operations.clear();
            return true;
        }
    }
}