package org.contrum.prisma.watch.antidupe;

import com.mongodb.client.model.Filters;
import lombok.Getter;
import lombok.Setter;
import org.bson.conversions.Bson;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.watch.PaperPrismaWatchService;
import org.contrum.prisma.watch.antidupe.item.WADItem;
import org.contrum.prisma.watch.antidupe.item.WADItemManager;
import org.contrum.prisma.watch.antidupe.listener.WADItemListener;
import org.contrum.prisma.watch.antidupe.listener.WADItemLoggerListener;
import org.contrum.prisma.watch.antidupe.processor.PlayerListProcessor;
import org.contrum.prisma.watch.antidupe.processor.utils.ProcessorSettings;
import org.contrum.prisma.watch.antidupe.tasks.ProcessorTask;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.*;

@Getter
public class WatchAntiDupeService {

    private static final String METADATA_BYPASS_ID = "prisma_watch_antidupe_bypass";
    private static final String MOVE_DUPED_ITEMS_PERMISSION = "core.antidupe.interact";

    private final PaperServices services;
    private final PaperPrismaWatchService prismaWatchService;

    private final WADItemManager itemManager;
    private final WADItemListener itemListener;
    private final WADItemLoggerListener itemLoggerListener;

    @Setter
    private boolean processEnabled = true;
    private boolean onlineProcessing = false;
    private boolean offlineProcessing = false;
    private final ScheduledFuture<?> processorTask;

    private final ScheduledExecutorService executor = Executors.newScheduledThreadPool(3, new AntiDupeThreadFactory());

    public WatchAntiDupeService(PaperPrismaWatchService prismaWatchService) {
        this.prismaWatchService = prismaWatchService;
        this.services = prismaWatchService.getServices();

        this.itemManager = new WADItemManager(this);
        this.itemListener = new WADItemListener(this);
        this.itemLoggerListener = new WADItemLoggerListener(this);

        System.out.println("[AntiDupe] Initializing AntiDupe Service...");
        processorTask = executor.scheduleAtFixedRate(new ProcessorTask(this), 1, 1, TimeUnit.SECONDS);
    }

    public void shutdown() {
        processorTask.cancel(false);
        itemManager.unload();
    }

    public void ensureThread(Runnable runnable) {
        if (this.isAntiDupeThread()) {
            runnable.run();
        } else {
            this.executor.execute(runnable);
        }
    }

    public boolean isAntiDupeThread() {
        return Thread.currentThread() instanceof AntiDupeThreadFactory.AntiDupeThread;
    }

    public boolean canProcessPlayer(OfflinePlayer player) {
        return !this.isBypass(player);
    }

    public boolean isAllowedToMoveDupedItems(Player player) {
        return player.hasPermission(MOVE_DUPED_ITEMS_PERMISSION);
    }

    public boolean isBypass(Player player) {
        return player.hasMetadata(METADATA_BYPASS_ID);
    }

    public boolean isBypass(OfflinePlayer player) {
        if (player.isOnline()) {
            return player.getPlayer().hasMetadata(METADATA_BYPASS_ID);
        }
        return false; // Offline players cannot have bypass metadata
    }

    public void setBypass(Player player, boolean bypass) {
        if (bypass) {
            player.setMetadata(METADATA_BYPASS_ID, new org.bukkit.metadata.FixedMetadataValue(services.getPlugin(), true));
            itemManager.resetInventory(player.getInventory());
        } else {
            player.removeMetadata(METADATA_BYPASS_ID, services.getPlugin());
        }
    }

    public boolean performOnlinePlayersSearch() {
        if (this.isOnlineProcessing()) return false;
        List<OfflinePlayer> list = Bukkit.getOnlinePlayers().stream().map(OfflinePlayer.class::cast).toList();
        this.onlineProcessing = true;
        this.ensureThread(() -> {
            PlayerListProcessor processor = new PlayerListProcessor(itemManager, list, new ProcessorSettings(false, false));
            processor.process();
            this.onlineProcessing = false;
        });

        return true;
    }

    public boolean performOfflinePlayerSearch() {
        if (this.isOfflineProcessing()) return false;
        this.offlineProcessing = true;
        this.ensureThread(() -> {
            System.out.println("Starting offline player...");
            try {
                List<OfflinePlayer> players = Arrays.stream(Bukkit.getOfflinePlayers()).toList();
                System.out.println("Starting offline player search for " + players.size() + " players.");
                PlayerListProcessor processor = new PlayerListProcessor(itemManager, players, new ProcessorSettings(true, true));
                processor.process();
                System.out.println("Offline player search completed.");
            } catch (Exception | Error e) {
                e.printStackTrace();
            }
            this.offlineProcessing = false;
        });

        return true;
    }

    public CompletableFuture<List<WADItem>> findItems(WADItemFilter filter) {
        Bson bsonFilter = filter.build(services);

        return itemManager.getMongoBackend().queryForCollection(WADItemManager.COLLECTION_NAME,
                (collection) -> {

                    List<WADItem> items = new ArrayList<>();

                    collection.find(bsonFilter).forEach((doc) -> {
                        WADItem item = new WADItem(doc);
                        items.add(item);
                    });

                    return items;
                }, executor
        );
    }

    @Getter
    public static class WADItemFilter {

        private Material material = null;
        private boolean onlyCurrentServer = true;
        private boolean onlyDuped = true;
        private long minDate = -1;
        private long maxDate = Long.MAX_VALUE;

        public static WADItemFilter create() {
            return new WADItemFilter();
        }

        public WADItemFilter material(@Nullable Material material) {
            this.material = material;
            return this;
        }

        public WADItemFilter onlyCurrentServer(boolean onlyCurrentServer) {
            this.onlyCurrentServer = onlyCurrentServer;
            return this;
        }

        public WADItemFilter onlyDuped(boolean onlyDuped) {
            this.onlyDuped = onlyDuped;
            return this;
        }

        public WADItemFilter minDate(long minDate) {
            this.minDate = minDate;
            return this;
        }

        public WADItemFilter maxDate(long maxDate) {
            this.maxDate = maxDate;
            return this;
        }

        public Bson build(PaperServices services) {
            List<Bson> filters = new ArrayList<>();

            // Material filter
            if (material != null) {
                filters.add(Filters.eq("type", material.name()));
            }

            // Server name filter
            if (onlyCurrentServer) {
                String serverName = services.getServersService().getCurrentServer().getName().toUpperCase();
                filters.add(Filters.regex("id", "^" + serverName + "-"));
            }

            // Only duped filter
            if (onlyDuped) {
                filters.add(Filters.eq("dupeMarked", true));
            }

            // Date filters
            if (minDate > 0) {
                filters.add(Filters.gte("lastInteractionDate", minDate));
            }
            if (maxDate < Long.MAX_VALUE) {
                filters.add(Filters.lte("lastInteractionDate", maxDate));
            }

            // Combine
            if (filters.isEmpty()) {
                return new org.bson.Document();
            }

            if (filters.size() == 1) {
                return filters.get(0);
            }
            return Filters.and(filters);
        }
    }
}
