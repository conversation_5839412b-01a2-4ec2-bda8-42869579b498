package org.contrum.prisma.watch.antidupe.listener;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityResurrectEvent;
import org.bukkit.event.entity.PlayerDeathEvent;
import org.bukkit.event.inventory.*;
import org.bukkit.event.player.PlayerDropItemEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerSwapHandItemsEvent;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.watch.antidupe.WatchAntiDupeService;
import org.contrum.prisma.watch.antidupe.item.WADItemManager;
import org.contrum.tritosa.Translator;

import java.util.Iterator;

public class WADItemListener implements Listener {

    private final PaperServices services;
    private final Translator translator;
    private final WatchAntiDupeService antiDupeService;
    private final WADItemManager itemManager;

    public WADItemListener(WatchAntiDupeService antiDupeService) {
        this.antiDupeService = antiDupeService;
        this.services = antiDupeService.getServices();
        this.translator = services.getTranslator();
        this.itemManager = antiDupeService.getItemManager();

        Bukkit.getPluginManager().registerEvents(this, this.services.getPlugin());
    }

    @EventHandler
    public void dupe(InventoryClickEvent event) {
        InventoryAction action = event.getAction();

        if (action == InventoryAction.CLONE_STACK) {
            ItemStack item = event.getCurrentItem();
            if (item == null) return;

            String id = itemManager.getId(item);
            if (id == null || itemManager.isDuped(item)) return;

            // Update item after click
            TaskUtil.run(services.getPlugin(), () -> {
                // Change id
                ItemStack itemOnCursor = event.getWhoClicked().getItemOnCursor();
                itemManager.changeId((OfflinePlayer) event.getWhoClicked(), itemOnCursor);
            });
        }
    }

    @EventHandler
    public void dupeCreative(InventoryCreativeEvent event) {
        ItemStack cursor = event.getCursor();
        if (cursor != null && !cursor.getType().isAir()) {

            String id = itemManager.getId(cursor);

            if (id == null || itemManager.isDuped(cursor)) return;

            // Check if there is any other item with the same id in the inventory
            boolean found = false;
            ItemStack[] contents = event.getWhoClicked().getInventory().getContents();
            for (ItemStack content : contents) {
                if (content == null) continue;

                String contentId = itemManager.getId(content);
                if (contentId == null) continue;

                if (contentId.equals(id)) {
                    found = true;
                    break;
                }
            }

            if (found) {
                // If found, we should create a new id for the item
                itemManager.changeId((OfflinePlayer) event.getWhoClicked(), cursor);
                event.setCursor(cursor);
                if (event.getSlot() > 0)
                    event.getWhoClicked().getInventory().setItem(event.getSlot(), cursor);
            }
        }
    }

    @EventHandler(ignoreCancelled = true)
    public void playerOpenShulker(InventoryOpenEvent event){
        if (event.getInventory().getType().equals(InventoryType.SHULKER_BOX)){
            Player player = (Player) event.getPlayer();

            //Get shulkerbox
            ItemStack shulker = player.getInventory().getItemInMainHand();
            if (!shulker.getType().toString().toLowerCase().contains("shulker")){
                shulker = player.getInventory().getItemInOffHand();
                if (!shulker.getType().toString().toLowerCase().contains("shulker")){
                    event.setCancelled(true);
                    return;
                }
            }

            // Cancel if shulker is duped
            if (itemManager.isDuped(shulker)){

                if (antiDupeService.isAllowedToMoveDupedItems(player)) {
                    translator.send(player, "ANTI_DUPE.SHULKER_BYPASS");
                    return;
                }

                translator.send(player, "ANTI_DUPE.DUPED_ITEM");
                event.setCancelled(true);
                return;
            }

            // Make sure the shulker box has an id
            if (!itemManager.hasId(shulker)){
                itemManager.ensureId(player, shulker);
                event.setCancelled(true);
            }
        }
    }

    @EventHandler(priority = EventPriority.LOW, ignoreCancelled = true)
    public void dropItemEvent(PlayerDropItemEvent event) {
        ItemStack stack = event.getItemDrop().getItemStack();
        if (itemManager.isDuped(stack)) {
            translator.send(event.getPlayer(), "ANTI_DUPE.DUPED_ITEM");
            event.setCancelled(true);
        }
    }

    @EventHandler(ignoreCancelled = true)
    public void itemClick(InventoryClickEvent event){
        ItemStack item = event.getCurrentItem();
        if (item == null || item.isEmpty()){
            if (event.getHotbarButton() >= 0) {
                item = event.getWhoClicked().getInventory().getItem(event.getHotbarButton());
            }
        }

        if (itemManager.isDuped(item)){
            Player player = (Player) event.getWhoClicked();
            if (antiDupeService.isBypass(player) || antiDupeService.isAllowedToMoveDupedItems(player)) {
                translator.send(event.getWhoClicked(), "ANTI_DUPE.DUPED_ITEM_BYPASS");
                return;
            }

            translator.send(event.getWhoClicked(), "ANTI_DUPE.DUPED_ITEM");
            event.setCancelled(true);
        }
    }

    @EventHandler(ignoreCancelled = true)
    public void itemSwap(PlayerSwapHandItemsEvent event) {
        ItemStack item = event.getMainHandItem();
        ItemStack item2 = event.getOffHandItem();

        if (itemManager.isDuped(item) || itemManager.isDuped(item2)) {
            Player player = event.getPlayer();
            if (antiDupeService.isBypass(player) || antiDupeService.isAllowedToMoveDupedItems(player)) {
                translator.send(player, "ANTI_DUPE.DUPED_ITEM_BYPASS");
                return;
            }

            translator.send(player, "ANTI_DUPE.DUPED_ITEM");
            event.setCancelled(true);
        }
    }
    
    @EventHandler(ignoreCancelled = true)
    public void onEntityResurrect(EntityResurrectEvent event) {
        if (event.getEntity() instanceof Player) {
            Player player = (Player) event.getEntity();
            ItemStack mainHand = player.getEquipment().getItemInMainHand();
            ItemStack offHand = player.getEquipment().getItemInOffHand();

            boolean totemInHand = mainHand.getType() == Material.TOTEM_OF_UNDYING || offHand.getType() == Material.TOTEM_OF_UNDYING;
            boolean isDupedItem = itemManager.isDuped(mainHand) || itemManager.isDuped(offHand);

            if (totemInHand && isDupedItem) {
                if (antiDupeService.isBypass(player)) {
                    translator.send(player, "ANTI_DUPE.DUPED_ITEM_BYPASS");
                    return;
                }

                translator.send(player, "ANTI_DUPE.DUPED_ITEM");
                event.setCancelled(true);
            }
        }
    }
    
    @EventHandler(ignoreCancelled = true)
    public void onPotionUse(PlayerInteractEvent event) {
        if (event.getItem() != null) {
            Material type = event.getItem().getType();
            if (type == Material.POTION || type == Material.SPLASH_POTION) {
                ItemStack potion = event.getItem();
                Player player = event.getPlayer();

                if (itemManager.isDuped(potion)) {
                    if (antiDupeService.isBypass(player) || antiDupeService.isAllowedToMoveDupedItems(player)) {
                        translator.send(player, "ANTI_DUPE.DUPED_ITEM_BYPASS");
                        return;
                    }

                    translator.send(player, "ANTI_DUPE.DUPED_ITEM");
                    event.setCancelled(true);
                }
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGHEST)
    public void playerDeath(PlayerDeathEvent event) {
        // Keep duped items
        Iterator<ItemStack> iterator = event.getDrops().iterator();
        while (iterator.hasNext()) {
            ItemStack item = iterator.next();
            if (item != null && itemManager.isDuped(item)) {
                // Keep item
                iterator.remove();
                event.getItemsToKeep().add(item);
            }
        }
    }
}