package org.contrum.prisma.watch.antidupe.item;

import com.github.retrooper.packetevents.PacketEvents;
import com.github.retrooper.packetevents.event.PacketListener;
import com.github.retrooper.packetevents.event.PacketListenerCommon;
import com.github.retrooper.packetevents.event.PacketListenerPriority;
import com.github.retrooper.packetevents.event.PacketSendEvent;
import com.github.retrooper.packetevents.protocol.component.ComponentTypes;
import com.github.retrooper.packetevents.protocol.nbt.NBTCompound;
import com.github.retrooper.packetevents.protocol.packettype.PacketType;
import com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerSetCursorItem;
import com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerSetPlayerInventory;
import com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerSetSlot;
import com.github.retrooper.packetevents.wrapper.play.server.WrapperPlayServerWindowItems;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.mongodb.client.model.Filters;
import com.mongodb.client.model.ReplaceOptions;
import lombok.Getter;
import org.bson.Document;
import org.bukkit.Bukkit;
import org.bukkit.NamespacedKey;
import org.bukkit.OfflinePlayer;
import org.bukkit.block.ShulkerBox;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.BlockStateMeta;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.persistence.PersistentDataType;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.mongo.MongoBackend;
import org.contrum.prisma.utils.InventoryUtils;
import org.contrum.prisma.utils.NBTUtil;
import org.contrum.prisma.utils.PlayerVaultsUtils;
import org.contrum.prisma.utils.ReflectionUtils;
import org.contrum.prisma.utils.user.SimpleUser;
import org.contrum.prisma.watch.antidupe.WatchAntiDupeService;
import org.contrum.prisma.watch.antidupe.item.log.WADItemLogChangeOwner;
import org.contrum.prisma.watch.antidupe.item.log.WADItemLogDupeMarked;
import org.jetbrains.annotations.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

@Getter
public class WADItemManager {

    public static final String KEY_ID = "wad_id";
    public static final String DUPED_ID = "wad_duped";
    public static final String COLLECTION_NAME = "prisma_watch_antidupe";

    private final PaperServices services;
    private final WatchAntiDupeService service;

    private final MongoBackend mongoBackend;

    private final Cache<String, WADItem> cachedItems = CacheBuilder.newBuilder()
            .maximumSize(500)
            .weakValues()
            .expireAfterAccess(5, TimeUnit.MINUTES)
            .build();
    private final PacketListenerCommon listener;

    public WADItemManager(WatchAntiDupeService service) {
        this.service = service;
        this.services = service.getServices();
        this.mongoBackend = services.getMongoBackend();

        listener = PacketEvents.getAPI().getEventManager().registerListener(new PacketListener() {
            @Override
            public void onPacketSend(PacketSendEvent event) {
                if (event.getPacketType() == PacketType.Play.Server.WINDOW_ITEMS) {
                    WrapperPlayServerWindowItems packet = new WrapperPlayServerWindowItems(event);
                    for (com.github.retrooper.packetevents.protocol.item.ItemStack item : packet.getItems()) {
                        if (item == null || item.isEmpty())
                            continue;

                        processItem(item);
                    }
                } else if (event.getPacketType() == PacketType.Play.Server.SET_SLOT) {
                    if (true) return;
                    WrapperPlayServerSetSlot packet = new WrapperPlayServerSetSlot(event);
                    com.github.retrooper.packetevents.protocol.item.ItemStack item = packet.getItem();
                    if (item != null && !item.isEmpty()) {
                        processItem(item);
                    }
                } else if (event.getPacketType() == PacketType.Play.Server.SET_CURSOR_ITEM) {
                    WrapperPlayServerSetCursorItem packet = new WrapperPlayServerSetCursorItem(event);
                    com.github.retrooper.packetevents.protocol.item.ItemStack item = packet.getStack();
                    if (item != null && !item.isEmpty()) {
                        processItem(item);
                    }
                } else if (event.getPacketType() == PacketType.Play.Server.SET_PLAYER_INVENTORY) {
                    WrapperPlayServerSetPlayerInventory packet = new WrapperPlayServerSetPlayerInventory(event);
                    com.github.retrooper.packetevents.protocol.item.ItemStack item = packet.getStack();
                    if (item != null && !item.isEmpty()) {
                        processItem(item);
                    }
                }
            }
        }, PacketListenerPriority.NORMAL);
    }

    private void processItem(com.github.retrooper.packetevents.protocol.item.ItemStack item) {
        NBTCompound nbtCompound = item.getComponent(ComponentTypes.CUSTOM_DATA).orElse(null);
        if (nbtCompound == null) {
            return;
        }

        NBTCompound publicBukkitValues = nbtCompound.getCompoundTagOrNull("PublicBukkitValues");
        if (publicBukkitValues != null && !publicBukkitValues.isEmpty()) {

            if (publicBukkitValues.getStringTagOrNull("prismacore:wad_id") == null) {
                return;
            }

            publicBukkitValues.removeTag("prismacore:wad_id");
            if (publicBukkitValues.isEmpty()) {
                nbtCompound.removeTag("PublicBukkitValues");
            } else {
                nbtCompound.setTag("PublicBukkitValues", publicBukkitValues);
            }

            item.setComponent(ComponentTypes.CUSTOM_DATA, nbtCompound);
        }
    }

    public void unload() {
        PacketEvents.getAPI().getEventManager().unregisterListener(listener);
    }

    public boolean isCompatibleItem(ItemStack item) {
        if (item == null) {
            return false;
        }

        return item.getMaxStackSize() == 1;
    }

    @Nullable
    public String ensureId(OfflinePlayer player, ItemStack item) {
        if (item == null) {
            throw new IllegalArgumentException("ItemStack cannot be null");
        }

        if (!this.isCompatibleItem(item)) {
            return null;
        }

        if (!hasId(item)) {
            String ID = this.generateUniqueItemID();
            NBTUtil.setNBT(item, KEY_ID, PersistentDataType.STRING, ID);

            // TODO: Check for already existing ID in the database
            this.createWADItem(player, item);
            return ID;
        }

        return NBTUtil.getNBT(item.getItemMeta(), KEY_ID, PersistentDataType.STRING, null);
    }

    public String changeId(OfflinePlayer player, ItemStack item) {
        if (item == null) {
            throw new IllegalArgumentException("ItemStack cannot be null");
        }

        if (!this.isCompatibleItem(item)) {
            return null;
        }

        String newId = this.generateUniqueItemID();
        NBTUtil.setNBT(item, KEY_ID, PersistentDataType.STRING, newId);

        WADItem wadItem = this.createWADItem(player, item);
        cachedItems.put(newId, wadItem);
        this.saveWADItem(wadItem);

        return newId;
    }

    public void resetItem(ItemStack item) {
        if (item == null || !item.hasItemMeta())
            return;

        ItemMeta itemMeta = item.getItemMeta();
        itemMeta.getPersistentDataContainer().remove(new NamespacedKey(services.getPlugin(), KEY_ID));
        itemMeta.getPersistentDataContainer().remove(new NamespacedKey(services.getPlugin(), DUPED_ID));
        item.setItemMeta(itemMeta);
    }

    public void resetInventory(Inventory inventory) {
        InventoryUtils.processItems(inventory.getContents(),
                item -> item != null && !item.isEmpty() && this.isCompatibleItem(item) && this.hasId(item),
                this::resetItem
        );
    }

    public String getId(ItemStack item) {
        if (item == null) {
            throw new IllegalArgumentException("ItemStack cannot be null");
        }

        if (!hasId(item)) {
            return null;
        }

        return NBTUtil.getNBT(item.getItemMeta(), KEY_ID, PersistentDataType.STRING, null);
    }

    public void markAsDuped(OfflinePlayer player, ItemStack item, WADItem.ItemLocation location, WADItemLogDupeMarked.Reason reason) {
        this.getOrCreateWADItem(player, item).whenComplete((wadItem, throwable) -> {
            if (throwable != null) {
                throwable.printStackTrace();
                return;
            }

            if (wadItem == null) {
                throw new IllegalStateException("WADItem should not be null after creation");
            }

            WADItemLogDupeMarked log = new WADItemLogDupeMarked(wadItem, item, location, new SimpleUser(player.getUniqueId(), player.getName()), reason);
            log.setData(player);

            this.setDuped(item, true);

            wadItem.addLog(log);
            this.saveWADItem(wadItem);
        });
    }

    private void setDuped(ItemStack item, boolean duped) {
        if (duped) {
            // Add lore
            List<String> lore = item.getLore();
            if (lore == null) {
                lore = new ArrayList<>();
            }
            lore.add(CC.translate("&c&l⛔"));
            lore.add(CC.translate("&c&l⛔ Item Dupeado"));
            lore.add(CC.translate("&c&l⛔"));

            item.setLore(lore);

            NBTUtil.setNBT(item, DUPED_ID, PersistentDataType.BOOLEAN, true);
        }
        else {
            // Remove lore
            List<String> lore = item.getLore();
            if (lore != null) {
                lore.removeIf(line -> line.contains("⛔"));
                item.setLore(lore);
            }

            NBTUtil.removeNBT(item, DUPED_ID);
        }
    }

    public boolean isDuped(ItemStack item) {
        if (item == null) {
            return false;
        }

        return item.hasItemMeta() && NBTUtil.getNBT(item.getItemMeta(), DUPED_ID, PersistentDataType.BOOLEAN, false);
    }

    public void addWADLog(OfflinePlayer player, ItemStack item, WADItemLog log, boolean save) {
        this.getOrCreateWADItem(player, item).whenComplete(((wadItem, throwable) -> {
            if (throwable != null) {
                throwable.printStackTrace();
                return;
            }

            if (wadItem == null) {
                throw new IllegalStateException("WADItem should not be null after creation");
            }

            wadItem.addLog(log);
            if (save)
                saveWADItem(wadItem);
        }));
    }

    public void addWADLog(OfflinePlayer player, ItemStack item, WADItemLog log) {
        this.addWADLog(player, item, log, true);
    }

    public void updateItemData(OfflinePlayer player, ItemStack item, WADItem.ItemLocation location) {
        this.getOrCreateWADItem(player, item).whenComplete((wadItem, throwable) -> {
            if (throwable != null) {
                throwable.printStackTrace();
                return;
            }

            if (wadItem == null) {
                throw new IllegalStateException("WADItem should not be null after creation");
            }

            boolean updated = false;

            UUID owner = wadItem.getCurrentOwner().getUniqueId();
            if (!owner.equals(player.getUniqueId())) {
                WADItemLogChangeOwner log = new WADItemLogChangeOwner(wadItem, new SimpleUser(player.getUniqueId(), player.getName()), WADItemLogChangeOwner.Reason.PROCESS_TASK);
                log.setData(player);

                wadItem.addLog(log);
                wadItem.setLocation(location);
                updated = true;
            } else if (!location.equals(wadItem.getLocation())) {
                wadItem.setLocation(location);
                updated = true;
            }

            if (updated)
                saveWADItem(wadItem);
        });
    }

    public void saveWADItem(WADItem item) {
        service.ensureThread(() -> {
            try {
                if (item == null) throw new IllegalArgumentException("WADItem cannot be null");
                Document document = item.serialize();

                mongoBackend.querySyncForCollection(COLLECTION_NAME, (collection) -> {
                    collection.replaceOne(Filters.eq("id", item.getID()), document, new ReplaceOptions().upsert(true));
                    return null;
                });
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    private WADItem createWADItem(OfflinePlayer player, ItemStack item) {
        String id = this.getId(item);

        WADItem newItem = new WADItem(item, new SimpleUser(player.getUniqueId(), player.getName()));
        cachedItems.put(id, newItem);
        this.saveWADItem(newItem);
        return newItem;
    }

    public CompletableFuture<WADItem> getOrCreateWADItem(OfflinePlayer player, ItemStack item) {
        if (!this.isCompatibleItem(item)) {
            return CompletableFuture.completedFuture(null);
        }

        String id = this.ensureId(player, item);

        return this.getWADItem(id)
                .thenApply(wadItem -> {
                    if (wadItem != null) {
                        return wadItem;
                    }

                    WADItem newItem = new WADItem(item, new SimpleUser(player.getUniqueId(), player.getName()));
                    cachedItems.put(id, newItem);
                    this.saveWADItem(newItem);
                    return newItem;
                });
    }

    public CompletableFuture<WADItem> getWADItem(String id) {
        WADItem cachedItem = cachedItems.getIfPresent(id);

        if (cachedItem != null) {
            return CompletableFuture.completedFuture(cachedItem);
        }

        return mongoBackend.queryForCollection(COLLECTION_NAME, (collection) -> {
            Document document = collection.find(Filters.eq("id", id)).limit(1).first();

            if (document == null) {
                return null;
            }

            WADItem item = new WADItem(document);
            cachedItems.put(id, item);

            return item;
        }).exceptionally(e -> {
            e.printStackTrace();
            return null;
        });
    }

    public boolean hasId(ItemStack item) {
        if (item == null || item.getItemMeta() == null) {
            return false;
        }

        return item.getItemMeta()
                .getPersistentDataContainer()
                .has(new NamespacedKey(services.getPlugin(), KEY_ID), PersistentDataType.STRING);
    }

    public CompletableFuture<ItemStack> findItem(String id, Function<ItemStack, Boolean> modifier) {
        return getWADItem(id).thenCompose(wadItem -> {
            if (wadItem == null || wadItem.getLocation() == null) return CompletableFuture.completedFuture(null);
            WADItem.ItemLocation loc = wadItem.getLocation();
            SimpleUser owner = wadItem.getCurrentOwner();
            InventoryData invData = getInventory(owner.getUniqueId(), owner.getName(), loc);
            if (invData == null || invData.inventory == null) return CompletableFuture.completedFuture(null);

            if (loc.isShulker()) {
                ItemStack container = invData.inventory.getItem(loc.getSlot());
                if (container == null || !(container.getItemMeta() instanceof BlockStateMeta meta)) return CompletableFuture.completedFuture(null);
                ShulkerBox shulker = (ShulkerBox) meta.getBlockState();
                ItemStack item = shulker.getInventory().getItem(loc.getShulkerSlot());
                if (!isSameItem(item, wadItem.getID())) return CompletableFuture.completedFuture(null);

                boolean modified = modifier.apply(item);
                if (modified) {
                    meta.setBlockState(shulker);
                    container.setItemMeta(meta);
                    invData.inventory.setItem(loc.getSlot(), container);
                    saveInventory(invData, loc);
                }
                return CompletableFuture.completedFuture(container);
            } else {
                ItemStack item = invData.inventory.getItem(loc.getSlot());
                if (!isSameItem(item, wadItem.getID())) return CompletableFuture.completedFuture(null);
                boolean modified = modifier.apply(item);
                if (modified) saveInventory(invData, loc);
                return CompletableFuture.completedFuture(item);
            }
        }).exceptionally(e -> { e.printStackTrace(); return null; });
    }

    public CompletableFuture<List<ItemStack>> findItems(List<String> ids, Function<ItemStack, Boolean> modifier) {
        List<CompletableFuture<ItemStack>> futures = ids.stream()
                .map(id -> findItem(id, modifier))
                .toList();
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream().map(f -> f.getNow(null)).filter(Objects::nonNull).toList());
    }

    public CompletableFuture<List<ItemStack>> findDupedItems(String id, Function<List<ItemStack>, Boolean> modifier) {
        return getWADItem(id).thenCompose(wadItem -> {
            if (wadItem == null || wadItem.getDupedCopies().isEmpty()) return CompletableFuture.completedFuture(new ArrayList<ItemStack>());
            List<CompletableFuture<ItemStack>> futures = wadItem.getDupedCopies().stream()
                    .map(duped -> retrieveItemStackFromDuped(duped, modifier))
                    .toList();
            return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures.stream().map(f -> f.getNow(null)).filter(Objects::nonNull).toList());
        }).exceptionally(e -> { e.printStackTrace(); return new ArrayList<>(); });
    }

    public CompletableFuture<List<ItemStack>> findDupedItemsWithNull(String id, Function<List<ItemStack>, Boolean> modifier) {
        return getWADItem(id).thenCompose(wadItem -> {
            if (wadItem == null || wadItem.getDupedCopies().isEmpty()) return CompletableFuture.completedFuture(new ArrayList<ItemStack>());
            List<CompletableFuture<ItemStack>> futures = wadItem.getDupedCopies().stream()
                    .map(duped -> retrieveItemStackFromDuped(duped, modifier))
                    .toList();
            return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .thenApply(v -> futures.stream().map(f -> f.getNow(null)).toList());
        }).exceptionally(e -> { e.printStackTrace(); return new ArrayList<>(); });
    }

    public CompletableFuture<List<ItemStack>> findDupedItemsForIds(List<String> ids, Function<List<ItemStack>, Boolean> modifier) {
        List<CompletableFuture<List<ItemStack>>> futures = ids.stream()
                .map(id -> findDupedItems(id, modifier))
                .toList();
        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream().flatMap(f -> f.getNow(List.of()).stream()).toList());
    }

    private CompletableFuture<ItemStack> retrieveItemStackFromDuped(DupedItem duped, Function<List<ItemStack>, Boolean> modifier) {
        WADItem.ItemLocation loc = duped.getLocation();
        SimpleUser owner = duped.getCurrentOwner();
        InventoryData invData = getInventory(owner.getUniqueId(), owner.getName(), loc);
        if (invData == null || invData.inventory == null) return CompletableFuture.completedFuture(null);

        if (loc.isShulker()) {
            ItemStack container = invData.inventory.getItem(loc.getSlot());
            if (container == null || !(container.getItemMeta() instanceof BlockStateMeta meta)) return CompletableFuture.completedFuture(null);
            ShulkerBox shulker = (ShulkerBox) meta.getBlockState();
            ItemStack item = shulker.getInventory().getItem(loc.getShulkerSlot());
            if (!isSameItem(item, duped.getID())) return CompletableFuture.completedFuture(null);

            boolean modified = modifier.apply(List.of(item));
            if (modified) {
                meta.setBlockState(shulker);
                container.setItemMeta(meta);
                invData.inventory.setItem(loc.getSlot(), container); // Update inventory metadata!
                saveInventory(invData, loc);
            }
            return CompletableFuture.completedFuture(container);
        } else {
            ItemStack item = invData.inventory.getItem(loc.getSlot());
            if (!isSameItem(item, duped.getID())) return CompletableFuture.completedFuture(null);
            boolean modified = modifier.apply(List.of(item));
            if (modified) saveInventory(invData, loc);
            return CompletableFuture.completedFuture(item);
        }
    }

    private static class InventoryData {
        Inventory inventory;
        Player loadedPlayer;
        OfflinePlayer offlinePlayer;
    }

    private InventoryData getInventory(UUID uuid, String name, WADItem.ItemLocation loc) {
        OfflinePlayer player = Bukkit.getOfflinePlayer(uuid);
        if (!player.hasPlayedBefore()) return null;
        Player loadedPlayer = player.isOnline() ? player.getPlayer() : ReflectionUtils.loadPlayerData(uuid, name);
        if (loadedPlayer == null && !player.isOnline()) return null;
        Inventory inv = switch (loc.getInventoryType()) {
            case MAIN -> loadedPlayer.getInventory();
            case ENDER_CHEST -> loadedPlayer.getEnderChest();
            case VAULT -> PlayerVaultsUtils.loadVault(uuid, loc.getVaultNumber());
            default -> null;
        };
        InventoryData data = new InventoryData();
        data.inventory = inv;
        data.loadedPlayer = loadedPlayer;
        data.offlinePlayer = player;
        return data;
    }

    private void saveInventory(InventoryData invData, WADItem.ItemLocation loc) {
        if (!invData.offlinePlayer.isOnline() && invData.loadedPlayer != null) {
            switch (loc.getInventoryType()) {
                case MAIN -> invData.loadedPlayer.getInventory().setContents(invData.inventory.getContents());
                case ENDER_CHEST -> invData.loadedPlayer.getEnderChest().setContents(invData.inventory.getContents());
            }
            invData.loadedPlayer.saveData();
        }
        if (loc.getInventoryType() == WADItem.InventoryType.VAULT)
            PlayerVaultsUtils.saveVault(invData.offlinePlayer.getUniqueId(), invData.inventory, loc.getVaultNumber());
    }

    private boolean isSameItem(ItemStack item, String expectedId) {
        return item != null && expectedId.equals(this.getId(item));
    }

    private String generateUniqueItemID() {
        String timestamp = Long.toHexString(System.currentTimeMillis());
        String uuid = UUID.randomUUID().toString().replace("-", "").substring(0, 16);
        String random = Integer.toHexString(ThreadLocalRandom.current().nextInt(0x10000));
        return (services.getServersService().getCurrentServer().getName() + "-" + timestamp + uuid + random).toUpperCase();
    }
}