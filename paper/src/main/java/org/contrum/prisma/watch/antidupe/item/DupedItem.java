package org.contrum.prisma.watch.antidupe.item;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.bson.Document;
import org.contrum.prisma.utils.serialize.DocumentSerialized;
import org.contrum.prisma.utils.user.SimpleUser;
import org.contrum.prisma.watch.antidupe.item.log.LightWeightItemData;

@RequiredArgsConstructor @Getter
public class DupedItem implements DocumentSerialized {
    private final String ID;
    private final LightWeightItemData item;
    private final WADItem.ItemLocation location;
    private final SimpleUser currentOwner;
    private long dupeDate = System.currentTimeMillis();

    public DupedItem(Document document) {
        this.ID = document.getString("id");
        this.item = new LightWeightItemData(document.get("item", Document.class));
        this.location = new WADItem.ItemLocation(document.get("location", Document.class));
        this.currentOwner = new SimpleUser(document.get("currentOwner", Document.class));
        this.dupeDate = document.getLong("dupeDate");
    }

    @Override
    public Document serialize() {
        return new Document()
                .append("id", ID)
                .append("item", item.serialize())
                .append("location", location.serialize())
                .append("currentOwner", currentOwner.serialize())
                .append("dupeDate", dupeDate);
    }
}
