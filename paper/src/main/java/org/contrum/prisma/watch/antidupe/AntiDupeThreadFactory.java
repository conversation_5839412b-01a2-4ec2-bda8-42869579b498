package org.contrum.prisma.watch.antidupe;

import lombok.NoArgsConstructor;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

@NoArgsConstructor
public class AntiDupeThreadFactory implements ThreadFactory {
    private final AtomicInteger threadNumber = new AtomicInteger(1);

    @Override
    public Thread newThread(Runnable r) {
        Runnable wrapped = () -> {
            try {
                r.run();
            } catch (Throwable t) {
                System.err.println("[AntiDupeThread] Uncaught exception:");
                t.printStackTrace();
            }
        };
        Thread t = new AntiDupeThread(wrapped, "AntiDupeThread-" + threadNumber.getAndIncrement());
        if (t.isDaemon()) t.setDaemon(false);
        if (t.getPriority() != Thread.NORM_PRIORITY) t.setPriority(Thread.NORM_PRIORITY);
        t.setUncaughtExceptionHandler((thread, throwable) -> {
            System.err.println("[AntiDupeThread] Uncaught exception in thread " + thread.getName());
            throwable.printStackTrace();
        });
        return t;
    }

    public static class AntiDupeThread extends Thread {
        public AntiDupeThread(Runnable runnable, String name) {
            super(runnable, name);
        }
    }
}
