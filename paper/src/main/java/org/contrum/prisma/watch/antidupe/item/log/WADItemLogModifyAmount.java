package org.contrum.prisma.watch.antidupe.item.log;

import org.bson.Document;
import org.contrum.prisma.watch.antidupe.item.WADItem;
import org.contrum.prisma.watch.antidupe.item.WADItemLog;

public class WADItemLogModifyAmount extends WADItemLog {

    private final int oldAmount;
    private final int newAmount;

    public WADItemLogModifyAmount(Document document) {
        super(document);

        this.oldAmount = document.getInteger("oldAmount");
        this.newAmount = document.getInteger("newAmount");
    }

    public WADItemLogModifyAmount(int oldAmount, int newAmount) {
        super(Type.MODIFY_AMOUNT);

        this.oldAmount = oldAmount;
        this.newAmount = newAmount;
    }

    @Override
    public String getNewData() {
        return newAmount + "";
    }

    @Override
    public String getOldData() {
        return oldAmount + "";
    }

    @Override
    protected String getExtraLogData() {
        return "";
    }

    @Override
    public void update(WADItem item) {
        super.update(item);
        item.setAmount(newAmount);
    }

    @Override
    public Document serialize() {
        return super.serialize()
                .append("oldAmount", oldAmount)
                .append("newAmount", newAmount);
    }
}
