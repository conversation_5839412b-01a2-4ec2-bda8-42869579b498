package org.contrum.prisma.tag.menu.edit;

import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.bukkit.Material;
import org.bukkit.conversations.ConversationContext;
import org.bukkit.conversations.Prompt;
import org.bukkit.conversations.StringPrompt;
import org.bukkit.entity.Player;
import org.contrum.chorpu.chat.CC;
import org.contrum.chorpu.chat.ChatUtils;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.Menu;
import org.contrum.chorpu.menu.impl.def.ConfirmMenu;
import org.contrum.chorpu.xseries.XMaterial;
import org.contrum.prisma.PrismaCore;
import org.contrum.prisma.prompts.menu.PromptSelectContextMenu;
import org.contrum.prisma.tag.Tag;
import org.contrum.prisma.tag.menu.SelectTagSectionMenu;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.util.Map;

@RequiredArgsConstructor
public class TagEditMenu extends Menu {

    private final PrismaCore plugin;
    private final Tag tag;

    @Override
    public int getRows(Player player) {
        return 3;
    }

    @Override
    public Map<Integer, Button> getButtons(Player player) {
        Map<Integer, Button> buttons = Maps.newHashMap();

        buttons.put(4, Button.of(new ItemBuilder(tag.getIcon())
                .setName(tag.getDisplayName())
                .addLore(
                        "&eName: &f" + tag.getName(),
                        "&eStatus: " + (tag.isEnabled() ? "&aenabled" : "&cdisabled"),
                        "&eSection: " + (tag.getSection() == null ? "&cnone" : tag.getSection().getDisplayName()),
                        "&eDisplayName: " + tag.getDisplayName(),
                        "&ePrefix: " + tag.getPrefix(),
                        "&eSuffix: " + tag.getSuffix(),
                        "&eServer context: &f" +  tag.getServerContext().toString()
                ).build()));

        buttons.put(getSlot(1, 1), Button.of(new ItemBuilder(tag.isEnabled() ? Material.REDSTONE_TORCH : Material.LEVER)
                .setName("&eToggle status")
                .addLore("&eStatus: " + (tag.isEnabled() ? "&aenabled" : "&cdisabled"))
                .build(), other -> {
            tag.setEnabled(!tag.isEnabled());
            player.sendMessage(CC.translate("&eStatus changed to " + (tag.isEnabled() ? "&aenabled" : "&cdisabled")));
            player.playSound(player.getLocation(), "ui.button.click", 1, 1);
        }));

        buttons.put(getSlot(1, 2), Button.of(new ItemBuilder(Material.ANVIL)
                .setName("&eRename tag display name")
                .addLore("&eClick to rename tag display name")
                .build(),
                other -> {
                    ChatUtils.beginCancellablePrompt(other, new StringPrompt() {
                        @NotNull
                        @Override
                        public String getPromptText(@NotNull ConversationContext conversationContext) {
                            return CC.translate("&eEnter new display name, or type &ccancel&e to cancel");
                        }

                        @Override
                        public @Nullable Prompt acceptInput(@NotNull ConversationContext context, @Nullable String input) {

                            if (input == null || input.equalsIgnoreCase("cancel")) {
                                open(player);
                                return Prompt.END_OF_CONVERSATION;
                            }

                            Player player = (Player) context.getForWhom();
                            tag.setDisplayName(input);
                            player.sendMessage(CC.translate("&eTag display name renamed to " + input));
                            player.playSound(player.getLocation(), "ui.button.click", 1, 1);
                            open(player);
                            return Prompt.END_OF_CONVERSATION;
                        }
                    }, plugin);
                }));

        buttons.put(getSlot(1, 3), Button.of(new ItemBuilder(Material.ANVIL)
                        .setName("&eRename tag")
                        .addLore("&eClick to rename tag")
                        .build(),
                other -> {
                    ChatUtils.beginCancellablePrompt(other, new StringPrompt() {
                        @NotNull
                        @Override
                        public String getPromptText(@NotNull ConversationContext conversationContext) {
                            return CC.translate("&eEnter new name, or type &ccancel&e to cancel");
                        }

                        @Override
                        public @Nullable Prompt acceptInput(@NotNull ConversationContext context, @Nullable String input) {

                            if (input == null || input.equalsIgnoreCase("cancel")) {
                                open(player);
                                return Prompt.END_OF_CONVERSATION;
                            }

                            Player player = (Player) context.getForWhom();

                            plugin.getPaperServices().getTagService().removeTag(tag);
                            tag.setName(input);
                            plugin.getPaperServices().getTagService().addTag(tag);

                            player.sendMessage(CC.translate("&eTag renamed to " + input));
                            player.playSound(player.getLocation(), "ui.button.click", 1, 1);
                            open(player);

                            return Prompt.END_OF_CONVERSATION;
                        }
                    }, plugin);
                }));

        buttons.put(getSlot(1, 5), Button.of(new ItemBuilder(tag.getSection() == null ? XMaterial.BARRIER.parseItem() : tag.getSection().getIcon())
                .setName("&eChange section")
                .addLore(
                        "&eCurrent Tag Section: " + (tag.getSection() == null ? "&cnone" : tag.getSection().getDisplayName()),
                        "&eClick to change section"
                ).build(),
                other -> {
                    new SelectTagSectionMenu(plugin,
                            null,
                            (menu, tagSection) ->  {
                        if (tag.getSection() != null) {
                            tag.getSection().removeTag(tag);
                        }

                        tag.setSection(tagSection);
                        tagSection.addTag(tag);
                        player.sendMessage(CC.translate("&eTag section changed to " + tagSection.getDisplayName()));
                        player.playSound(player.getLocation(), "ui.button.click", 1, 1);
                        open(player);
                    }).open(other);
                }));

        buttons.put(getSlot(1, 6), Button.of(new ItemBuilder(Material.PAPER)
                .setName("&eChange prefix")
                .addLore(
                        "&eCurrent prefix: " + tag.getPrefix(),
                        "&eClick to change prefix"
                ).build(), other -> {
           ChatUtils.beginPrompt(other, new StringPrompt() {
               @NotNull
               @Override
               public String getPromptText(@NotNull ConversationContext conversationContext) {
                   return CC.translate(CC.translate("&eEnter new prefix, or type &ccancel&e to cancel"));
               }

               @Override
               public @Nullable Prompt acceptInput(@NotNull ConversationContext context, @Nullable String input) {

                   if (input == null || input.equalsIgnoreCase("cancel" )) {
                       open(player);
                       return Prompt.END_OF_CONVERSATION;
                   }

                   Player player = (Player) context.getForWhom();
                   tag.setPrefix(input);
                   player.sendMessage(CC.translate("&eTag prefix changed to " + input));
                   player.playSound(player.getLocation(), "ui.button.click", 1, 1);
                   open(player);
                   return Prompt.END_OF_CONVERSATION;
               }
           }, plugin);
        }));

        buttons.put(getSlot(1, 7), Button.of(new ItemBuilder(Material.PAPER)
                .setName("&eChange suffix")
                .addLore(
                        "&eCurrent suffix: " + tag.getSuffix(),
                        "&eClick to change suffix"
                ).build(), other -> {
           ChatUtils.beginPrompt(other, new StringPrompt() {
               @NotNull
               @Override
               public String getPromptText(@NotNull ConversationContext conversationContext) {
                   return CC.translate(CC.translate("&eEnter new suffix, or type &ccancel&e to cancel"));
               }

               @Override
               public @Nullable Prompt acceptInput(@NotNull ConversationContext context, @Nullable String input) {

                   if (input == null || input.equalsIgnoreCase("cancel" )) {
                       open(player);
                       return Prompt.END_OF_CONVERSATION;
                   }

                   Player player = (Player) context.getForWhom();
                   tag.setSuffix(input);
                   player.sendMessage(CC.translate("&eTag suffix changed to " + input));
                   player.playSound(player.getLocation(), "ui.button.click", 1, 1);
                   open(player);
                   return Prompt.END_OF_CONVERSATION;
               }
           }, plugin);
        }));

        buttons.put(getSlot(1, 4), Button.of(new ItemBuilder(Material.HOPPER)
                .setName("&eSelect server context")
                .addLore(
                        "&eClick to select where tag can be equip"
                ).build(), other -> {
            new PromptSelectContextMenu(plugin.getPaperServices(), serverContext -> {
                tag.setServerContext(serverContext);
                player.sendMessage(CC.translate("&a" + serverContext.toString() + " &eset as server context"));
                player.playSound(player.getLocation(), "ui.button.click", 1, 1);
                open(player);
            }).open(player);
        }));

        buttons.put(getSlot(2, 4), Button.of(new ItemBuilder(tag.getIcon())
                .setName("&eChange icon")
                .addLore(
                        "&eClick to change icon"
                ).build(), other -> {
            ChatUtils.beginPrompt(other, new StringPrompt() {
                @NotNull
                @Override
                public String getPromptText(@NotNull ConversationContext conversationContext) {
                    return CC.translate(CC.translate("&ePut an item in your hand and type &aconfirm&e or &ccancel&e to cancel"));
                }

                @Override
                public @Nullable Prompt acceptInput(@NotNull ConversationContext context, @Nullable String input) {
                    if (input == null || input.equalsIgnoreCase("cancel")) {
                        open(player);
                        return Prompt.END_OF_CONVERSATION;
                    }

                    Player player = (Player) context.getForWhom();

                    if (player.getInventory().getItemInMainHand().getType() == Material.AIR) {
                        player.sendMessage(CC.translate("&cYou must put an item in your hand"));
                        return this;
                    }

                    tag.setIcon(player.getInventory().getItemInMainHand());
                    player.sendMessage(CC.translate("&eTag icon changed to " + tag.getIcon().getType().name()));
                    player.playSound(player.getLocation(), "ui.button.click", 1, 1);
                    open(player);
                    return Prompt.END_OF_CONVERSATION;
                }
            }, plugin);
        }));

        buttons.put(getSlot(2, 8), Button.of(new ItemBuilder(Material.BARRIER).setName("&cDelete tag").build(), other -> {
            new ConfirmMenu(remove -> {
                if (remove) {
                    plugin.getPaperServices().getTagService().removeTag(tag);
                    plugin.getPaperServices().getTagService().updateTag(tag, true);
                    player.sendMessage(CC.translate("&eTag " + tag.getName() + " &cdeleted"));
                    player.playSound(player.getLocation(), "ui.button.click", 1, 1);
                } else {
                    open(player);
                }
            }, new ItemBuilder(tag.getIcon())
                    .setName("&cDelete tag " + tag.getName()).build()).open(player);
        }));

        return buttons;
    }

    @Override
    public String getTitle(Player player) {
        return "Editing " + tag.getName();
    }

    @Override
    public void onClose() {
        plugin.getPaperServices().getTagService().updateTag(tag);
    }
}
