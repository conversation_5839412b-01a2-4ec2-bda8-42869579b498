package org.contrum.prisma.lootbox;

import lombok.Getter;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.lootbox.boxes.LootBox;
import org.contrum.prisma.lootbox.boxes.LootBoxes;
import org.contrum.prisma.lootbox.listener.LootBoxListener;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.NBTUtil;
import org.contrum.prisma.utils.config.ConfigFile;
import org.contrum.tritosa.Translator;

import java.util.HashMap;
import java.util.Map;

@Getter
public class LootBoxService {
    private final PaperServices services;
    private final Translator translator;
    private ConfigFile config;

    @Getter
    private final Map<LootBoxes, LootBox> lootBoxes = new HashMap<>();

    public LootBoxService(PaperServices services) {
        this.services = services;
        this.translator = services.getTranslator();
        this.config = new ConfigFile(services.getPlugin(), "data/lootbox.yml");
        this.loadLootBoxes();

        new LootBoxListener(this);
    }

    public void reload() {
        try {
            this.config = new ConfigFile(services.getPlugin(), "data/lootbox.yml");
            this.loadLootBoxes();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public boolean openLootBox(LootBoxes lootBoxType, Player player) {
        LootBox lootBox = lootBoxes.get(lootBoxType);
        if (lootBox == null) {
            translator.send(player, "LOOT_BOXES.NOT_FOUND");
            return false;
        }

        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        if (!metadata.getOwnedLootBoxes().remove(lootBoxType.name())) {
            translator.send(player, "LOOT_BOXES.NOT_OWNED", lootBoxType.name().toLowerCase());
            return false;
        }

        lootBox.open(services, player);
        return true;
    }

    public ItemStack getLootBoxItem(LootBoxes lootBoxType) {
        LootBox lootBox = lootBoxes.get(lootBoxType);
        if (lootBox == null) {
            return null;
        }

        ItemStack item = lootBox.getItem();
        NBTUtil.setNBT(item, "lootbox", PersistentDataType.STRING, lootBoxType.name());
        return item;
    }

    public void addLootBox(ProfilePaperMetadata metadata, LootBoxes lootBoxType) {
        metadata.getOwnedLootBoxes().add(lootBoxType.name());
    }

    public void addLootBox(Player player, LootBoxes lootBoxType) {
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        this.addLootBox(metadata, lootBoxType);
    }

    public void loadLootBoxes() {
        lootBoxes.clear();

        ConfigurationSection section = config.getConfig().getConfigurationSection("LOOT_BOXES");
        if (section == null) {
            return;
        }

        for (LootBoxes lootBoxType : LootBoxes.values()) {
            ConfigurationSection boxSection = section.getConfigurationSection(lootBoxType.name());
            if (boxSection == null)
                continue;

            LootBox lootBox = lootBoxType.createInstance(services, boxSection);
            lootBoxes.put(lootBoxType, lootBox);
        }
    }
}