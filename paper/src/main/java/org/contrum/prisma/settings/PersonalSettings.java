package org.contrum.prisma.settings;

import lombok.Data;
import org.bson.Document;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.contrum.prisma.settings.impl.*;
import org.contrum.prisma.utils.GeyserUtil;
import org.contrum.prisma.utils.serialize.DocumentSerialized;

import java.lang.reflect.InvocationTargetException;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Data
public class PersonalSettings implements DocumentSerialized {

    private final Map<Class<? extends Setting>, Setting<?>> settings = new HashMap<>(Map.of( // SettingID -> Setting
            ArmorTradeMode.class, new ArmorTradeMode(),
            PrivateMessageMode.class, new PrivateMessageMode(),
            FPSMode.class, new FPSMode(),
            PayMode.class, new PayMode(),
            CoinsMode.class, new CoinsMode(),
            ChatMode.class, new ChatMode(),
            BedrockFormsMode.class, new BedrockFormsMode()
    ));

    public PersonalSettings(UUID uuid) {
        Player player = Bukkit.getPlayer(uuid);
        if (player == null) {
            return;
        }

        if (GeyserUtil.isGeyser(player)) {
            this.getSetting(ArmorTradeMode.class).setValue(null, ArmorTradeMode.Value.MERCHANT);
        }
    }

    public <T extends Setting<?>> T getSetting(Class<T> clazz) {
        return (T) settings.computeIfAbsent(clazz, k -> {
            try {
                return clazz.getDeclaredConstructor().newInstance();
            } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                     NoSuchMethodException e) {
                throw new RuntimeException(e);
            }
        });
    }

    public PersonalSettings(Document document) {
        document.forEach((key, value) -> {
            try {
                Class<?> clazz = Class.forName(key.replaceAll("_", "."));
                Setting<?> setting = (Setting<?>) clazz.getConstructor(Document.class).newInstance((Document) value);
                settings.put(setting.getClass(), setting);
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    @Override
    public Document serialize() {
        return settings.entrySet().stream()
                .collect(Document::new, (doc, entry) -> { // Save as key: SettingClass, value: SettingDocument
                    doc.append(entry.getValue().getClass().getName().replaceAll("\\.", "_"), entry.getValue().serialize());
                }, Document::putAll);
    }
}