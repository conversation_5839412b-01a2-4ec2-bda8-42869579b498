package org.contrum.prisma.settings.impl;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.bson.Document;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.settings.Setting;
import org.contrum.prisma.settings.SettingCategory;
import org.contrum.prisma.settings.SettingEnum;

import java.util.List;

public class PayMode extends Setting<PayMode.Value> {
    private static final String DISPLAY_NAME = "Modo de Pagos";
    private static final Material MATERIAL = Material.GOLD_INGOT;
    private static final List<String> DESCRIPTION = CC.translate(List.of(
            "&7Activa o bloquea la opción de recibir",
            "&7dinero de otros jugadores con &e/pay&7."
    ));

    public PayMode() {
        super(Value.ON);
    }

    public PayMode(Document document) throws ClassNotFoundException {
        super(document, Value.ON);
    }

    public PayMode(Value value) {
        super(value);
    }

    @Override
    public String getId() {
        return "payMode";
    }

    @Override
    public SettingCategory getCategory() {
        return SettingCategory.PROFILE;
    }

    @Override
    public String getDisplayName() {
        return DISPLAY_NAME;
    }

    @Override
    public List<String> getDescription() {
        return DESCRIPTION;
    }

    @Override
    public Material getDisplayMaterial() {
        return MATERIAL;
    }

    @RequiredArgsConstructor @Getter
    public enum Value implements SettingEnum {
        ON("Activado", Material.LIME_DYE, ChatColor.YELLOW),
        OFF("Desactivado", Material.RED_DYE, ChatColor.GREEN);

        private final String settingName;
        private final Material settingMaterial;
        private final ChatColor settingColor;
    }
}
