package org.contrum.prisma.spawn.commands;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.spawn.SpawnService;
import org.contrum.chorpu.chat.CC;

@CommandAlias("setspawn")
@CommandPermission("core.command.setspawn")
public class SetSpawnCommand extends BaseCommand {

    @Dependency private SpawnService spawnService;
    @Dependency private PaperServices services;

    @Default
    @Syntax("[world]")
    public void setSpawn(Player sender, @Optional String worldName) {
        if (worldName == null) {
            worldName = sender.getWorld().getName();
        }

        World world = sender.getServer().getWorld(worldName);
        Location location = sender.getLocation().clone();
        Location eyeLocation = sender.getEyeLocation();

        location.setYaw(eyeLocation.getYaw());
        location.setPitch(eyeLocation.getPitch());

        spawnService.setSpawn(worldName, location);
        world.setSpawnLocation(location);
        sender.sendMessage(CC.translate("&eSpawn of the world &6" + worldName + " &eset."));
    }

}
