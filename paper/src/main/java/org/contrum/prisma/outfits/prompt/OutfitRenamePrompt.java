package org.contrum.prisma.outfits.prompt;

import lombok.RequiredArgsConstructor;
import org.bukkit.conversations.ConversationContext;
import org.bukkit.conversations.Prompt;
import org.bukkit.conversations.StringPrompt;
import org.bukkit.entity.Player;
import org.contrum.tritosa.Translator;

import java.util.function.Consumer;

@RequiredArgsConstructor
public class OutfitRenamePrompt extends StringPrompt {

    private final Translator translator;
    private final Consumer<String> onFinish;

    @Override
    public String getPromptText(ConversationContext conversationContext) {
        Player player = (Player) conversationContext.getForWhom();
        return translator.getAsText(player, "OUTFITS.RENAME_PROMPT.MESSAGE");
    }

    @Override
    public Prompt acceptInput(ConversationContext context, String input) {
        Player player = (Player) context.getForWhom();
        if (input == null) {
            translator.send(player, "OUTFITS.RENAME_PROMPT.INVALID_NAME");
            return this;
        }

        if (input.equalsIgnoreCase("cancel") || input.equalsIgnoreCase("cancelar")) {
            onFinish.accept(null);
            return Prompt.END_OF_CONVERSATION;
        }

        onFinish.accept(input);
        return Prompt.END_OF_CONVERSATION;
    }
}
