package org.contrum.prisma.profile.garbage;

import com.google.common.reflect.TypeToken;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.bson.Document;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.utils.serialize.DocumentSerialized;

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

@Getter @NoArgsConstructor
public class ProfileGarbage implements DocumentSerialized {
    private List<ItemStack> items = new ArrayList<>();

    public ProfileGarbage(Document document) {
        Type type = new TypeToken<List<ItemStack>>(){}.getType();

        if (document.containsKey("items")) {
            this.items = PaperServices.GSON.fromJson(document.getString("items"), type);
        }
    }

    public void addItem(PaperServices services, ItemStack item) {
        if (this.hasItem(services, item)) {
            return;
        }
        item.setAmount(1);
        this.items.add(item);
    }

    public void removeItem(ItemStack item) {
        this.items.remove(item);
    }

    public boolean hasItem(PaperServices services, ItemStack item) {
        for (ItemStack itemStack : this.items) {
            if (this.areSame(services, itemStack, item)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Document serialize() {
        Type type = new TypeToken<List<ItemStack>>(){}.getType();
        return new Document()
                .append("items", PaperServices.GSON.toJson(items, type));
    }

    private boolean areSame(PaperServices services, ItemStack i1, ItemStack i2) {
        if (i1 == null || i2 == null || i1.getType().equals(Material.AIR) || i2.getType().equals(Material.AIR)) return false;

        //Compare type
        if (i1.getType() != i2.getType()) return false;

        //Compare customarmor
        if (services.getCustomArmorService().isArmor(i1) || services.getCustomArmorService().isArmor(i2)) {
            CustomArmor a1 = services.getCustomArmorService().getArmor(i1);
            CustomArmor a2 = services.getCustomArmorService().getArmor(i2);

            if (a1 == null || a2 == null) return false;
            return a1.getName().equals(a2.getName());
        }

        if (Boolean.compare(i1.hasItemMeta(), i2.hasItemMeta()) != 0) return false;

        return i1.getLore() == i2.getLore();
    }
}