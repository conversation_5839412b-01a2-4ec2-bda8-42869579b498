package org.contrum.prisma.profile.garbage.menu;

import lombok.RequiredArgsConstructor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.PaginatedMenu;
import org.contrum.chorpu.menu.storage.StorageMenu;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.customarmor.CustomArmor;
import org.contrum.prisma.customarmor.CustomArmorService;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.profile.garbage.ProfileGarbage;
import org.contrum.prisma.utils.ItemBuilder1_20;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RequiredArgsConstructor
public class GarbageSelectArmorMenu extends PaginatedMenu {

    private final ProfileService service;
    private final ProfileGarbage garbage;

    @Override
    public List<Button> getPaginatedButtons(Player player) {
        List<Button> buttons = new ArrayList<>();

        CustomArmorService armorService = ((PaperServices) service.getServiceManager()).getCustomArmorService();
        for (CustomArmor customArmor : armorService.getArmorsSorted()) {
            List<ItemStack> armorItems = armorService.getSet(customArmor);
            ItemStack item = !customArmor.getSkullSkin().isEmpty() ? armorItems.get(0) : armorItems.get(4);
            ItemBuilder1_20 builder = new ItemBuilder1_20(item);
            builder.setLore("");
            builder.addLore("&b&l| &eClick &cizquierdo &epara agregar la &barmadura");
            builder.addLore("&b&l| &eClick &cderecho &epara agregar &dcompress y bloques");

            buttons.add(Button.of(builder.build(), (c, type) -> {
                if (type.isLeftClick())
                    armorItems.forEach(i -> garbage.addItem((PaperServices) service.getServiceManager(), i));
                else if (type.isRightClick() || type.name().contains("DROP")) {
                    garbage.addItem((PaperServices) service.getServiceManager(), armorService.getArmorCompress(customArmor, 1));
                    garbage.addItem((PaperServices) service.getServiceManager(), armorService.getArmorBlock(customArmor, 1));
                } else {
                    return;
                }

                new GarbageEditMenu(service, garbage).open(c);
            }));
        }

        return buttons;
    }

    @Override
    public Map<Integer, Button> getGlobalButtons(Player player) {
        Map<Integer, Button> buttons = new HashMap<>();

        buttons.put(36, Button.of(new ItemBuilder(Material.RED_BED).setName("&cVolver").build(), (c) -> {
            new GarbageEditMenu(service, garbage).open(c);
        }));

        return buttons;
    }

    @Override
    public int getRows(Player player) {
        return 5;
    }

    @Override
    public StorageMenu.FillType getFillType() {
        return StorageMenu.FillType.ONLY_CORNERS;
    }

    @Override
    public String getTitle(Player player) {
        return "Garbage";
    }
}
