package org.contrum.prisma.coinflip.menus;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryType;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.coinflip.CoinFlipBet;
import org.contrum.prisma.coinflip.CoinFlipService;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.tritosa.Translator;

public class CoinFlipItemMenu implements Listener {
    private final CoinFlipService service;
    private final Translator translator;
    private final Player player;
    private ItemStack currentItem;
    private Inventory inventory;

    public CoinFlipItemMenu(CoinFlipService service, Player player) {
        this.service = service;
        this.translator = service.getServices().getTranslator();

        this.player = player;
        Bukkit.getPluginManager().registerEvents(this, service.getServices().getPlugin());
        open();
    }

    private void open() {
        inventory = Bukkit.createInventory(null, 27, CC.translate("&6Coinflip item!"));
        ItemStack blackGlass = new ItemStack(Material.BLACK_STAINED_GLASS_PANE);
        ItemStack emeraldBlock = new ItemBuilder1_20(Material.EMERALD_BLOCK).name("&aAceptar").build();
        for (int i = 0; i < inventory.getSize(); i++) {
            inventory.setItem(i, blackGlass);
        }
        inventory.setItem(13, null);
        inventory.setItem(26, emeraldBlock);

        player.openInventory(inventory);
    }

    private void update() {
        inventory.setItem(13, currentItem);
    }

    @EventHandler
    public void inventoryClickEvent(InventoryClickEvent event) {
        if (event.getInventory().hashCode() == this.inventory.hashCode()) {
            event.setCancelled(true);
            if (event.getClickedInventory() == null) return;
            ItemStack item = event.getCurrentItem();
            if (item == null || item.getType().equals(Material.AIR) || item.getType().equals(Material.BLACK_STAINED_GLASS_PANE))
                return;
            //Player side
            if (event.getClickedInventory().getType().equals(InventoryType.PLAYER)) {
                if (currentItem != null) {
                    player.getInventory().addItem(currentItem);
                    currentItem = item;
                    event.setCurrentItem(null);
                    update();
                    return;
                }
                currentItem = item;
                event.setCurrentItem(null);
                update();
                return;
            } else {
                if (item.getType().equals(Material.EMERALD_BLOCK)) {
                    if (currentItem == null || currentItem.getType().equals(Material.AIR)) {
                        translator.send(player, "COIN_FLIP.NOT_ITEM_ADDED");
                        return;
                    }
                    CoinFlipBet bet = service.createItemBet(player, currentItem.clone());

                    if (bet != null) {
                        translator.send(player, "COIN_FLIP.CREATED_ITEMS");
                        currentItem = null;
                    }
                    player.closeInventory();
                } else {
                    if (currentItem != null) {
                        player.getInventory().addItem(currentItem);
                        currentItem = null;
                        update();
                    }
                }
            }
        }
    }

    @EventHandler
    public void playerCloseInventory(InventoryCloseEvent event) {
        if (event.getPlayer().getUniqueId().equals(player.getUniqueId()) && event.getInventory().hashCode() == this.inventory.hashCode()) {
            if (currentItem != null) {
                event.getPlayer().getInventory().addItem(currentItem);
            }
            HandlerList.unregisterAll(this);
        }
    }
}
