package org.contrum.prisma.coinflip.menus;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.PlayerChatEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.persistence.PersistentDataType;
import org.contrum.chorpu.chat.CC;
import org.contrum.chorpu.chat.ChatUtils;
import org.contrum.prisma.coinflip.CoinFlipBet;
import org.contrum.prisma.coinflip.CoinFlipBetType;
import org.contrum.prisma.coinflip.CoinFlipService;
import org.contrum.prisma.coinflip.prompt.CoinFlipBetAmountPrompt;
import org.contrum.prisma.profile.currency.Currency;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.NBTUtil;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class CoinFlipListMenu implements Listener {
    private final CoinFlipService service;
    private final Translator translator;

    private final Player player;
    private final CoinFlipBetType type;

    private Inventory inventory;

    public CoinFlipListMenu(CoinFlipService service, Player player, CoinFlipBetType type) {
        this.service = service;
        this.translator = service.getServices().getTranslator();

        this.player = player;
        this.type = type;
        open();
    }

    private void open() {
        Bukkit.getPluginManager().registerEvents(this, service.getServices().getPlugin());
        inventory = Bukkit.createInventory(null, 54, CC.translate("&6&lCoinflip"));

        ItemStack blackGlass = new ItemStack(Material.BLACK_STAINED_GLASS_PANE);
        ItemStack bedItem = new ItemBuilder1_20(Material.RED_BED).name("&cVolver").build();
        ItemStack emeraldItem = new ItemBuilder1_20(Material.EMERALD_BLOCK)
                .name(translator.getAsText(player, "COIN_FLIP.MENUS.LIST_MENU.CREATE_ITEM.NAME"))
                .lore(translator.getListString(player, "COIN_FLIP.MENUS.LIST_MENU.CREATE_ITEM.LORE"))
                .build();

        for (int i = 0; i < inventory.getSize(); i++) {
            if (i < 8 || i % 9 == 0 || (i - 8) % 9 == 0 || i >= 45) {
                inventory.setItem(i, blackGlass);
            }
        }

        List<CoinFlipBet> bets = new ArrayList<>();
        if (type.equals(CoinFlipBetType.COINS)) bets.addAll(service.getCoinsBets());
        if (type.equals(CoinFlipBetType.ITEM)) bets.addAll(service.getItemsBets());

        for (CoinFlipBet bet : bets) {
            int i = inventory.firstEmpty();
            if (i < 0) continue;
            Player playerOwner = Bukkit.getPlayer(bet.getOwner());
            if (playerOwner == null) continue;

            ItemBuilder1_20 builder = new ItemBuilder1_20(Material.PLAYER_HEAD);
            builder.name(translator.getAsText(player, "COIN_FLIP.MENUS.LIST_MENU.BET_ITEM.NAME", playerOwner));
            builder.setOwnerUUID(bet.getOwner());

            builder.lore(translator.getListString(player, "COIN_FLIP.MENUS.LIST_MENU.BET_ITEM.LORE", LocalPlaceholders.builder().add("<bet_string>", bet.getBetString())));
            builder.addNBT("BET_UUID", PersistentDataType.STRING, bet.getOwner().toString());

            inventory.setItem(i, builder.build());
        }

        inventory.setItem(49, emeraldItem);
        inventory.setItem(53, bedItem);
        player.openInventory(inventory);
    }

    @EventHandler
    public void inventoryClickEvent(InventoryClickEvent event) {
        if (event.getWhoClicked().getUniqueId().equals(player.getUniqueId()) && event.getInventory().hashCode() == this.inventory.hashCode()) {
            event.setCancelled(true);
            ItemStack item = event.getCurrentItem();
            if (item == null) return;

            //Back
            if (item.getType().equals(Material.RED_BED)) {
                new CoinFlipMainMenu(service, player);
                return;
            }
            if (item.getType().equals(Material.EMERALD_BLOCK)) {
                if (service.getActiveBets().containsKey(player.getUniqueId())) {
                    translator.send(player, "COIN_FLIP.ALREADY_CREATED");
                    return;
                }
                if (type.equals(CoinFlipBetType.COINS)) {
                    ChatUtils.beginPrompt(player, new CoinFlipBetAmountPrompt(service, service.getTranslator(), (amount) -> {
                        if (amount == -1) {
                            open();
                            return;
                        }

                        service.createCoinBet(player, amount);
                        HandlerList.unregisterAll(this);
                        translator.send(player, "COIN_FLIP.CREATED_COINS", LocalPlaceholders.builder().add("<amount>", amount + ""));
                    }), service.getServices().getPlugin());
                    player.closeInventory();
                    return;
                } else {
                    new CoinFlipItemMenu(service, player);
                }
                return;
            }
            if (item.getType().equals(Material.PLAYER_HEAD)) {
                if (!item.hasItemMeta()) return;

                UUID uuid = UUID.fromString(NBTUtil.getNBT(item.getItemMeta(), "BET_UUID", PersistentDataType.STRING, "00000000-0000-0000-0000-000000000000"));
                if (uuid.equals(player.getUniqueId())) {
                    translator.send(player, "COIN_FLIP.CANNOT_INVITE_SELF");
                    return;
                }
                CoinFlipBet bet = service.getActiveBets().get(uuid);
                if (bet == null) return;
                player.closeInventory();
                bet.makeBet(player);
            }
        }
    }

    @EventHandler(ignoreCancelled = true)
    public void playerCloseInventory(InventoryCloseEvent event) {
        if (event.getPlayer().getUniqueId().equals(player.getUniqueId()) && event.getInventory().hashCode() == this.inventory.hashCode()) {
            HandlerList.unregisterAll(this);
        }
    }
}
