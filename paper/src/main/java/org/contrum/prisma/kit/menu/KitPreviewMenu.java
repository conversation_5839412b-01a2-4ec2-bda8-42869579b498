package org.contrum.prisma.kit.menu;

import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.chat.CC;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.Menu;
import org.contrum.prisma.kit.Kit;

import java.util.Collections;
import java.util.Map;

@RequiredArgsConstructor
public class KitPreviewMenu extends Menu {

    private final Kit kit;
    private final Menu previousMenu;

    @Override
    public String getTitle(Player player) {
        return "&ePreviewing " + kit.getName();
    }

    @Override
    public int getRows(Player player) {
        return 6;
    }

    @Override
    public Map<Integer, Button> getButtons(Player player) {

        Map<Integer, Button> buttons = Maps.newHashMap();

        buttons.put(45, Button.of(new ItemBuilder(Material.RED_BED).setName("&cGo Back!").build(), other -> previousMenu.open(player)));

        buttons.put(53, Button.of(new ItemBuilder(!kit.canEquip(player) ? Material.REDSTONE : Material.EMERALD)
                .setName(!kit.canEquip(player) ? ChatColor.RED + "You don't own this kit" : ChatColor.GREEN + "You have access to this kit")
                .setLore(!kit.canEquip(player) ? Collections.singletonList(CC.translate("&eAvailable for purchase at &6tienda.prismamc.net")) : null)
                .build()));

        if (kit.getArmor() != null && kit.getArmor().length == 4) {
            ItemStack helmet = kit.getArmor()[3];
            ItemStack chestplate = kit.getArmor()[2];
            ItemStack leggings = kit.getArmor()[1];
            ItemStack boots = kit.getArmor()[0];

            if (helmet != null && helmet.getType() != Material.AIR) buttons.put(47, Button.of(helmet.clone()));

            if (chestplate != null && chestplate.getType() != Material.AIR)
                buttons.put(48, Button.of(chestplate.clone()));

            if (leggings != null && leggings.getType() != Material.AIR)
                buttons.put(50, Button.of(leggings.clone()));

            if (boots != null && boots.getType() != Material.AIR) buttons.put(51, Button.of(boots.clone()));
        }

        int index = 0;
        if (kit.getContents() != null) {
            for (ItemStack item : kit.getContents()) {
                if (index >= 53) {
                    break;
                }

                if (item != null && item.getType() != Material.AIR) {
                    ItemBuilder builder = new ItemBuilder(item.clone());

                    builder.addLore("");
                    builder.addLore("&cThis is preview of kit.");

                    item = builder.build();

                    buttons.put(index, Button.of(item));
                }

                index++;
            }
        }

        for (int i = 0; i <= 53; i++) {
            if (buttons.get(i) == null) {
                buttons.put(i, Button.of(new ItemBuilder(Material.RED_STAINED_GLASS_PANE).setName(" ").build()));
            }
        }

        return buttons;
    }
}
