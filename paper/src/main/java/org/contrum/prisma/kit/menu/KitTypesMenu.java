package org.contrum.prisma.kit.menu;

import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.Menu;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.NumberUtils;

import java.util.Map;

@RequiredArgsConstructor
public class KitTypesMenu extends Menu {

    private final PaperServices paperServices;

    @Override
    public String getTitle(Player player) {
        return "&e&lGKits Menu";
    }

    @Override
    public int getRows(Player player) {
        return 5;
    }

    @Override
    public Map<Integer, Button> getButtons(Player player) {

        Map<Integer, Button> buttons = Maps.newHashMap();

        short orangeData = 1;
        short yellowData = 4;

        ItemBuilder glass = new ItemBuilder(Material.RED_STAINED_GLASS_PANE)
                .setName(" ")
                .setGlowing(true);

        for (int i = 0; i < 9; i++) {
            buttons.put(i, Button.of(glass.setDurability(NumberUtils.isEven(i) ? orangeData : yellowData).build()));
        }

        buttons.put(getSlot(0, 1), Button.of(glass.setDurability(yellowData).build()));

        buttons.put(getSlot(8, 1), Button.of(glass.setDurability(orangeData).build()));

        for (int i = 0; i < 9; i++) {
            buttons.put(getSlot(i, 2), Button.of(glass.setDurability(NumberUtils.isEven(i) ? orangeData : yellowData).build()));
        }

        buttons.put(getSlot(3, 1), Button.of(new ItemBuilder(Material.IRON_INGOT)
                        .setName("&a&lFree &egKits").build(),
                (other) -> new KitListMenu(paperServices).open(other)));

        buttons.put(getSlot(5, 1), Button.of(new ItemBuilder(Material.DIAMOND)
                        .setName("&6&lPremium &egKits").build(),
                (other) -> new KitListMenu(paperServices).open(other)));

        return buttons;
    }
}
