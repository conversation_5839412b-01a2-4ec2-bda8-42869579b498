package org.contrum.prisma.kit;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@Getter
public enum KitType {

    PREMIUM("Premium"),
    FREE("Free");

    private final String name;

    public static KitType getByName(String name) {
        for (KitType kitType : values()) {
            if (kitType.getName().equalsIgnoreCase(name)) {
                return kitType;
            }
        }
        return null;
    }

}
