package org.contrum.prisma.listener;

import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.event.ClickEvent;
import net.kyori.adventure.text.event.HoverEvent;
import net.kyori.adventure.text.minimessage.MiniMessage;
import org.bson.Document;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.Listener;
import org.contrum.chorpu.chat.CC;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.commands.user.joinme.packet.JoinMePacket;
import org.contrum.prisma.commands.user.media.packets.NewVideoPacket;
import org.contrum.prisma.commands.user.media.packets.StreamPacket;
import org.contrum.prisma.grant.Grant;
import org.contrum.prisma.grant.packet.PlayerGrantPacket;
import org.contrum.prisma.permissions.PaperPermissionHandler;
import org.contrum.prisma.placeholders.OnDemandUserPlaceholders;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.chat.ChatType;
import org.contrum.prisma.profile.packet.SpecialChatPacket;
import org.contrum.prisma.profile.packet.UserHelpopPacket;
import org.contrum.prisma.profile.packet.UserReportPacket;
import org.contrum.prisma.rank.Rank;
import org.contrum.prisma.rank.packets.RankPacket;
import org.contrum.prisma.redis.packet.BroadcastPacket;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.prisma.server.packet.ServerPacket;
import org.contrum.prisma.utils.ComponentsUtils;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.redis.pyrite.packet.PacketContainer;
import org.contrum.prisma.utils.redis.pyrite.packet.RedisPacketListener;
import org.contrum.prisma.utils.user.SimpleUser;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;
import org.contrum.tritosa.utils.LegacyText;

import java.util.List;
import java.util.stream.Collectors;

public class ServerListener implements Listener, PacketContainer {

    private final PaperServices paperServices;
    private final Translator translator;

    public ServerListener(PaperServices paperServices) {
        this.paperServices = paperServices;
        this.translator = paperServices.getTranslator();
    }

    @RedisPacketListener
    public void onBroadcastPacket(BroadcastPacket packet) {
        for (String message : packet.getMessages()) {
            Bukkit.broadcastMessage(CC.translate(message));
        }
    }

    @RedisPacketListener
    public void onReceiveServerPacket(ServerPacket event) {
        PrismaServer server = new PrismaServer(Document.parse(event.getJson()));

        switch (event.getAction()) {
            case START -> PlayerUtils.getStaffOnline().forEach(p -> p.sendMessage(ComponentsUtils.textToComponent("<yellow>Server <green>"
                    + server.getName() + "<yellow> has <green>started<yellow>!")));
            case STOP -> PlayerUtils.getStaffOnline().forEach(p -> p.sendMessage(ComponentsUtils.textToComponent("<yellow>Server <green>"
                    + server.getName() + "<yellow> has <red>stopped<yellow>!")));
            case UPDATE -> {
                PrismaServer currentServer = paperServices.getServersService().getServer(server.getName());

                if (currentServer == null || currentServer.getStatus() == PrismaServer.ServerStatus.OFFLINE) {
                    PlayerUtils.getStaffOnline().forEach(p -> p.sendMessage(ComponentsUtils.textToComponent("<yellow>Server <green>"
                            + server.getName() + "<yellow> has <green>started<yellow>!")));
                }
            }
        }
    }

    @RedisPacketListener
    public void onRankUpdatePermissions(RankPacket packet) {
        RankPacket.RankAction action = packet.getAction();
        if (action == RankPacket.RankAction.UPDATE_PERMISSIONS || action == RankPacket.RankAction.UPDATE_INHERITS) {
            Rank rank = new Rank(Document.parse(packet.getJson()), paperServices.getRankService());

            for (Profile profile : paperServices.getProfileService().getOnlineProfiles().values()) {
                if (profile.getActiveGrant().getRank().getName().equals(rank.getName())) {
                    Player player = Bukkit.getPlayer(profile.getUniqueId());

                    if (player != null) {
                        PaperPermissionHandler permissionHandler = (PaperPermissionHandler) profile.getPermissionHandler();
                        permissionHandler.update();
                    }
                }
            }
        }
    }

    @RedisPacketListener
    public void onPlayerGrant(PlayerGrantPacket packet) {
        Profile profile = paperServices.getProfileService().getProfile(packet.getTarget());
        if (profile == null) return;

        Grant grant = packet.getGrant();
        if (grant == null) return;

        profile.getGrants().add(grant);
        profile.updateActiveGrant(paperServices);

        Player target = Bukkit.getPlayer(packet.getTarget());
        if (target == null) return;

        Rank rank = grant.getRank();
        if (rank == null) return;

        PaperPermissionHandler permissionHandler = (PaperPermissionHandler) profile.getPermissionHandler();
        permissionHandler.update();

        translator.send(profile, "COMMANDS.GRANT.RECEIVED", rank);
    }

    @RedisPacketListener
    public void onNewVideoPacket(NewVideoPacket packet) {
        translator.broadcast("NEW_VIDEO_BROADCAST",
                LocalPlaceholders.builder().add("<player_name>", packet.getPlayerName()).add("<link>", packet.getLink()));
    }

    @RedisPacketListener
    public void onStreamPacket(StreamPacket packet) {
        List<CommandSender> recipients = Bukkit.getOnlinePlayers().stream().map((p) -> (CommandSender) p).collect(Collectors.toList());
        recipients.add(Bukkit.getConsoleSender());

        for (CommandSender recipient : recipients) {
            translator.send(recipient, "STREAM_BROADCAST",
                    LocalPlaceholders.builder().add("<user_displayname>", packet.getPlayerName()).add("<link>", packet.getLink()));
        }
    }

    @RedisPacketListener
    public void onHelpopRequest(UserHelpopPacket packet) {
        SimpleUser simpleUser = packet.getUser();
        String message = packet.getReason();

        translator.broadcastForPermission("core.staff", "COMMANDS.HELPOP_COMMAND_NOTIFICATION",
                simpleUser,
                packet.getServer(),
                LocalPlaceholders.builder().add("<reason>", message));
    }

    @RedisPacketListener
    public void onReport(UserReportPacket packet) {
        SimpleUser user = packet.getUser();
        SimpleUser target = packet.getTarget();
        PrismaServer server = packet.getServer();
        String reason = packet.getReason();

        translator.broadcastForPermission("core.staff", "COMMANDS.REPORT.STAFF_BROADCAST",
                user,
                OnDemandUserPlaceholders.with("target", target),
                packet.getServer(),
                LocalPlaceholders.builder().add("<reason>", reason));
    }

    @RedisPacketListener
    public void onJoinMeRequest(JoinMePacket packet) {
        String message = packet.getMessage();
        String hoverMessage = packet.getHoverMessage();
        String clickCommand = packet.getClickCommand();

        Component component = Component.text(message)
                .clickEvent(ClickEvent.runCommand(clickCommand))
                .hoverEvent(HoverEvent.showText(MiniMessage.miniMessage().deserialize(LegacyText.convertToModern(hoverMessage))));
        Bukkit.broadcast(component);
    }

    @RedisPacketListener
    public void onSpecialChat(SpecialChatPacket packet) {
        SimpleUser user = packet.getUser();
        String message = packet.getMessage();
        ChatType type = packet.getType();
        PrismaServer server = packet.getServer();

        Bukkit.getOnlinePlayers().forEach(player -> {

            Profile profile = paperServices.getProfileService().getProfile(player.getUniqueId());
            if (profile == null || !type.getPredicate().test(profile)) return;

            paperServices.getTranslator().send(player,
                    type.getFormat(),
                    user,
                    server,
                    LocalPlaceholders.builder().add("<message>", message));
        });
    }
}
