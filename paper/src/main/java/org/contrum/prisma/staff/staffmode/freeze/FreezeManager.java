package org.contrum.prisma.staff.staffmode.freeze;

import com.google.common.collect.Maps;
import lombok.Getter;
import org.bukkit.Particle;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.metadata.FixedMetadataValue;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.PrismaCore;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.chat.ChatType;
import org.contrum.prisma.staff.staffmode.StaffModeService;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;
import org.jetbrains.annotations.Nullable;

import java.util.HashMap;
import java.util.UUID;

@Getter
public class FreezeManager {
    private final PrismaCore plugin;
    private final PaperServices services;
    private final Translator translator;
    private final StaffModeService staffModeService;
    private final HashMap<UUID, FrozenPlayer> frozenPlayers = Maps.newHashMap();

    public FreezeManager(PaperServices services, StaffModeService staffModeService) {
        this.services = services;
        this.plugin = staffModeService.getPlugin();
        this.translator = staffModeService.getTranslator();
        this.staffModeService = staffModeService;
    }

    public void toggleFreeze(Player by, Player target) {
        this.freeze(by, target, !this.isFreeze(target));
    }

    public void freeze(@Nullable Player by, Player target, boolean enable) {
        LocalPlaceholders placeholders = LocalPlaceholders.builder()
                .add("<staff_name>", by == null ? "unknown" : by.getName())
                .add("<user_name>", target.getName());

        Profile targetProfile = services.getProfileService().getProfile(target.getUniqueId());
        if (enable) {
            target.setMetadata("freeze", new FixedMetadataValue(plugin, true));
            target.addPotionEffect(new PotionEffect(PotionEffectType.SLOWNESS, Integer.MAX_VALUE, 200));
            target.spawnParticle(Particle.ELDER_GUARDIAN, target.getLocation(), 1);
            target.playSound(target.getLocation(), Sound.ENTITY_ELDER_GUARDIAN_CURSE, 1, 1.2F);

            targetProfile.setChatType(ChatType.FREEZE);

            if (frozenPlayers.containsKey(target.getUniqueId())) {
                frozenPlayers.get(target.getUniqueId()).destroy();
            }
            this.frozenPlayers.put(target.getUniqueId(), new FrozenPlayer(services.getHologramsService(), translator, target, by));

            if (by != null) {
                translator.send(target, "STAFF.FREEZE.FREEZE", placeholders);
                this.staffModeService.broadcastToStaff("STAFF.FREEZE.FREEZE_BROADCAST", placeholders);
            }
        } else {
            target.removeMetadata("freeze", plugin);
            target.removePotionEffect(PotionEffectType.SLOWNESS);

            if (targetProfile.getChatType().equals(ChatType.FREEZE)) {
                targetProfile.setChatType(ChatType.PUBLIC);
                services.getProfileService().saveProfile(targetProfile);
            }

            FrozenPlayer frozenPlayer = frozenPlayers.remove(target.getUniqueId());
            if (frozenPlayer != null) {
                frozenPlayer.destroy();
            }

            if (by != null) {
                translator.send(target, "STAFF.FREEZE.UNFREEZE", placeholders);
                this.staffModeService.broadcastToStaff("STAFF.FREEZE.UNFREEZE_BROADCAST", placeholders);
            }
        }
    }

    public boolean isFreeze(Player player) {
        return player.hasMetadata("freeze");
    }
}
