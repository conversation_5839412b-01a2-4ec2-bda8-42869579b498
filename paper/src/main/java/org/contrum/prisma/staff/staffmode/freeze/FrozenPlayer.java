package org.contrum.prisma.staff.staffmode.freeze;

import lombok.Getter;
import lombok.Setter;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.contrum.prisma.holograms.Hologram;
import org.contrum.prisma.holograms.HologramHandler;
import org.contrum.prisma.holograms.HologramsService;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.time.Instant;
import java.util.List;

@Getter @Setter
public class FrozenPlayer {
    private final Translator translator;
    private final Player player;
    private final Player staff;
    private Instant frozenAt;
    private Hologram hologram;

    public FrozenPlayer(HologramsService hologramsService, Translator translator, Player player, Player staff) {
        this.translator = translator;
        this.player = player;
        this.staff = staff;

        this.frozenAt = Instant.now();

        LocalPlaceholders placeholders = LocalPlaceholders.builder().add("<time>", this.getFormattedFrozenTime()).add("<staff_name>", staff == null ? "CONSOLE" : staff.getName());
        //Create hologram
        Location loc = player.getLocation().add(0, 3, 0);

        HologramHandler handler = hologramsService.getHandler();
        handler.destroyHologram("freeze-" + player.getName());
        hologram = handler.createHologram("freeze-" + player.getName(), loc, false, translator.getListString(staff, "STAFF.FREEZE.HOLOGRAM", placeholders));
    }

    public String getFormattedFrozenTime() {
        return TimeUtils.getFormattedTime(Instant.now().toEpochMilli() - frozenAt.toEpochMilli());
    }

    public void destroy() {
        this.hologram.destroy();
        this.hologram = null;
    }

    public void update() {
        if (hologram != null && player.isOnline()) {
            LocalPlaceholders placeholders = LocalPlaceholders.builder().add("<time>", this.getFormattedFrozenTime()).add("<staff_name>", staff == null ? "CONSOLE" : staff.getName());
            List<String> lines = translator.getListString(staff, "STAFF.FREEZE.HOLOGRAM", placeholders);
            hologram.setLines(0, lines);
            Location loc = player.getLocation().add(0, 3, 0);
            hologram.setLocation(loc);
        }
    }
}
