package org.contrum.prisma.systems.impl.anniversary.menu;

import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.contrum.chorpu.TaskUtil;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.storage.StorageMenu;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.systems.impl.anniversary.AnniversaryDay;
import org.contrum.prisma.systems.impl.anniversary.AnniversarySystem;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.menus.CoreMenu;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.HashMap;
import java.util.Map;

public class AnniversaryMissionsMenu extends CoreMenu {

    private final AnniversarySystem system;
    private final ProfilePaperMetadata metadata;

    private boolean blocked = false;

    public AnniversaryMissionsMenu(AnniversarySystem system, ProfilePaperMetadata metadata) {
        super(system.getServices());

        this.system = system;
        this.metadata = metadata;
    }

    @Override
    public int getRows(Player player) {
        return 6;
    }

    @Override
    public Map<Integer, Button> getButtons(Player player) {
        Map<Integer, Button> buttons = new HashMap<>();

        Translator translator = getServices().getTranslator();
        buttons.put(12, Button.of(new ItemBuilder1_20(Material.FIREWORK_ROCKET)
                .name(translator.getAsText(player, "MENUS.ANNIVERSARY.FIREWORK.NAME"))
                .lore(translator.getListString(player, "MENUS.ANNIVERSARY.FIREWORK.LORE"))
                .build()));
        buttons.put(13, Button.of(new ItemBuilder1_20(Material.CAKE)
                .name(translator.getAsText(player, "MENUS.ANNIVERSARY.CAKE.NAME"))
                .lore(translator.getListString(player, "MENUS.ANNIVERSARY.CAKE.LORE"))
                .build()));
        buttons.put(14, Button.of(new ItemBuilder1_20(Material.FIREWORK_ROCKET)
                .name(translator.getAsText(player, "MENUS.ANNIVERSARY.FIREWORK.NAME"))
                .lore(translator.getListString(player, "MENUS.ANNIVERSARY.FIREWORK.LORE"))
                .build()));

        ItemBuilder1_20 mainMissionItem = new ItemBuilder1_20(Material.END_CRYSTAL)
                .name(translator.getAsText(player, "MENUS.ANNIVERSARY.MAIN_MISSION.NAME"))
                .lore(translator.getListString(player, "MENUS.ANNIVERSARY.MAIN_MISSION.LORE",
                        LocalPlaceholders.builder()
                                .add("<cakes>", system.getCakesCount(metadata) + "")));

        if (system.isGlobalMissionCompleted(metadata) && !system.isGlobalMissionClaimed(metadata)) {
            mainMissionItem.addUnsafeEnchantment(Enchantment.PROTECTION, 1);
            mainMissionItem.addLore("");
            mainMissionItem.addLore("&aClick para reclamar!");
        } else if (system.isGlobalMissionCompleted(metadata)) {
            mainMissionItem.addLore("");
            mainMissionItem.addLore("&cYa reclamada!");
        }

        mainMissionItem.hideInformation();

        buttons.put(31, Button.of(mainMissionItem.build(),
                (other) -> {
                    if (system.isGlobalMissionCompleted(metadata) && !system.isGlobalMissionClaimed(metadata)) {
                        system.setGlobalMissionClaimed(metadata);
                        system.getMainMissionDay().giveRewards(getServices(), other);
                        WorldUtils.playSound(Sound.ENTITY_PLAYER_LEVELUP, other);
                        translator.send(other, "ANNIVERSARY.MISSIONS.GLOBAL.CLAIMED");
                        translator.broadcast("ANNIVERSARY.MISSIONS.GLOBAL.CLAIMED_BROADCAST", other);
                    } else if (system.isGlobalMissionCompleted(metadata)) {
                        translator.send(other, "ANNIVERSARY.MISSIONS.GLOBAL.ALREADY_CLAIMED");
                    } else {
                        translator.send(other, "ANNIVERSARY.MISSIONS.GLOBAL.NOT_COMPLETED", LocalPlaceholders.builder().add("<cakes>", system.getCakesCount(metadata) + ""));
                    }
                }
        ));

        int slot = 37;
        for (int i = 0; i < 7; i++) {
            AnniversaryDay day = system.getDay(i);

            Material material = Material.CHEST_MINECART;
            if (system.getTodayId() == i) {
                material = Material.TNT_MINECART;
            }

            ItemBuilder1_20 builder = new ItemBuilder1_20(material)
                    .name(day.getName())
                    .lore(day.getLore());

            if (system.isMissionCompleted(player, day.getMission())) {
                builder.setMaterial(Material.MINECART);
                if (system.getTodayId() == i && !system.hasClaimedDay(player, i)) {
                    builder.addUnsafeEnchantment(Enchantment.UNBREAKING, 1);
                    builder.setMaterial(Material.CHEST);
                    builder.addLore("");
                    builder.addLore("&aClick para reclamar!");
                } else {
                    builder.addLore("");
                    builder.addLore("&cYa reclamada");
                }
            } else if (system.getTodayId() > i) {
                builder.addLore("");
                builder.addLore("&cMision bloqueada");
            }

            builder.hideInformation();
            int id = i;
            buttons.put(slot + i, Button.of(builder.build(), (other) -> {
                if (blocked) return;
                if (system.isMissionCompleted(player, day.getMission()) && system.getTodayId() == id && !system.hasClaimedDay(player, id)) {
                    this.blocked = true;

                    translator.send(other, "ANNIVERSARY.CLAIMING");
                    system.canClaimToday(other).whenComplete((result, ex) -> {

                        if (!result) {
                            translator.send(other, "ANNIVERSARY.MAX_ALTS_REACHED");
                            this.blocked = false;
                            return;
                        }

                        TaskUtil.run(super.getServices().getPlugin(), () -> {
                            if (!system.isMissionCompleted(player, day.getMission()) || system.getTodayId() != id || system.hasClaimedDay(player, id)) {
                                this.blocked = false;
                                return;
                            }

                            try {
                                system.setDayClaimed(other, id);
                                day.giveRewards(getServices(), other);
                                WorldUtils.playSound(Sound.ENTITY_PLAYER_LEVELUP, player);
                                translator.send(player, "ANNIVERSARY.DAY_CLAIMED");
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                            this.blocked = false;
                        });
                    }).exceptionally(ex -> {
                        ex.printStackTrace();
                        return null;
                    });
                }
            }));
        }

        return buttons;
    }

    @Override
    public String getTitle(Player player) {
        return "Recompensas de Aniversário";
    }

    @Override
    public boolean isAutoUpdate() {
        return false;
    }

    @Override
    public StorageMenu.FillType getFillType() {
        return StorageMenu.FillType.ALL;
    }
}