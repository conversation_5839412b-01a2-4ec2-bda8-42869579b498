package org.contrum.prisma.systems.impl.darkweb;

import org.bukkit.Location;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.systems.SystemListener;

public class DarkWebSystem extends SystemListener {

    private Location npcSpawnLocation = null;

    public DarkWebSystem(PaperServices services) {
        super(services);
    }

    @Override
    public String getConfigID() {
        return "DARK_WEB";
    }
}