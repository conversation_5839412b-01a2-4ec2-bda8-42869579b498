package org.contrum.prisma.systems.impl.regionevents.impl;

import com.sk89q.worldedit.bukkit.BukkitWorld;
import com.sk89q.worldedit.regions.CuboidRegion;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Item;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityPickupItemEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.metadata.FixedMetadataValue;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.events.CurrencyAddEvent;
import org.contrum.prisma.profile.currency.Currency;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.systems.impl.regionevents.Region;
import org.contrum.prisma.systems.impl.regionevents.TimedRegionEvent;
import org.contrum.prisma.utils.ProbabilityUtil;
import org.contrum.prisma.utils.SafeTask;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public class CoinsRainEvent extends TimedRegionEvent implements Listener {

    private final static int LOCATIONS = 10;

    private final long NORMAL_VALUE = 2000;
    private final long BIG_VALUE = 5000;

    private List<Location> locations = new ArrayList<>();

    public CoinsRainEvent(Region region) {
        super(region);
    }

    @Override
    public Duration getDefaultDuration() {
        return Duration.ofMinutes(2);
    }

    @Override
    public String getID() {
        return "COINS_RAIN";
    }

    @Override
    protected void onStart() {
        // Find random locations
        CuboidRegion region = super.getRegion().getRegion();
        if (region == null || !(region.getWorld() instanceof BukkitWorld bukkitWorld)) {
            System.out.println("[CoinsRainEvent] Region is not a cuboid region or world is not a BukkitWorld.");
            super.getRegion().finishCurrentEvent(false, StopReason.NO_REQUIREMENTS);
            return;
        }
        World world = bukkitWorld.getWorld();

        this.locations.clear();
        for (int i = 0; i < LOCATIONS; i++) {
            Location loc = null;
            int tries = 0;
            while (loc == null && tries < 10) {
                // Get random x,z
                int x = region.getMinimumPoint().getBlockX() + (int) (Math.random() * (region.getMaximumPoint().getBlockX() - region.getMinimumPoint().getBlockX()));
                int z = region.getMinimumPoint().getBlockZ() + (int) (Math.random() * (region.getMaximumPoint().getBlockZ() - region.getMinimumPoint().getBlockZ()));

                // Get y
                loc = this.findValidCoinLocation(new Location(world, x, region.getMinimumY(), z));
                tries++;
            }

            if (loc != null) {
                this.locations.add(loc);
            }
        }

        Bukkit.getPluginManager().registerEvents(this, super.getRegion().getServices().getPlugin());
    }

    @Override
    protected void onStop(StopReason reason) {
        HandlerList.unregisterAll(this);

        // Remove all coin items
        for (Entity entity : super.getRegion().getBukkitWorld().getEntities()) {
            if (entity instanceof Item item && item.hasMetadata("coins_rain")) {
                SafeTask.ensureSync(super.getServices().getPlugin(), item::remove);
            }
        }
    }

    @Override
    protected void onTick(int tick) {
        if (tick % (20*1) == 0) {
            TaskUtil.run(super.getServices().getPlugin(),this::dropCoin);
        }
    }

    private void dropCoin() {
        if (locations.isEmpty()) return;

        Location location = this.locations.get(ThreadLocalRandom.current().nextInt(locations.size()));
        location = location.clone().add(0,1,0);

        boolean isBig = ProbabilityUtil.probability(25);
        Item item = location.getWorld().dropItem(location, new ItemStack(isBig ? Material.GOLD_INGOT : Material.GOLD_NUGGET));
        item.setUnlimitedLifetime(true);
        item.setMetadata("coins_rain", new FixedMetadataValue(super.getServices().getPlugin(), isBig ? BIG_VALUE : NORMAL_VALUE));
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGH)
    public void onPickup(EntityPickupItemEvent event) {
        Item item = event.getItem();
        if (event.getEntity() instanceof Player player && item.hasMetadata("coins_rain")) {
            item.setItemStack(new ItemStack(Material.AIR));

            long value = item.getMetadata("coins_rain").get(0).asLong();
            ProfilePaperMetadata metadata = super.getServices().getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
            metadata.depositCurrency(super.getServices().getPlugin(), player, Currency.COINS, CurrencyAddEvent.Cause.REGIONS_EVENT, value);

            WorldUtils.playPlingSong(player);
            super.sendLangMessage(player, "COINS_PICKUP", LocalPlaceholders.builder().add("<coins>", value + ""));
        }
    }

    private Location findValidCoinLocation(Location start) {
        World world = start.getWorld();

        int maxY = world.getMaxHeight();
        int x = start.getBlockX();
        int z = start.getBlockZ();

        for (int y = start.getBlockY(); y < maxY - 1; y++) {
            Location loc1 = new Location(world, x, y, z);
            Location loc2 = new Location(world, x, y + 1, z);
            if (world.getBlockAt(loc1).isEmpty() && world.getBlockAt(loc2).isEmpty()) {
                return loc1.add(0.5, 0, 0.5);
            }
        }
        return null;
    }
}
