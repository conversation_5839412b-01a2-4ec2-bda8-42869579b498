package org.contrum.prisma.systems.impl.dtc.impl;

import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.Sound;
import org.bukkit.entity.Creeper;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.systems.impl.dtc.DTC;
import org.contrum.prisma.systems.impl.dtc.DTCSystem;
import org.contrum.prisma.utils.WorldUtils;

public class BlockDTC extends DTC implements Listener {
    public BlockDTC(DTCSystem system) {
        super(system);
    }

    @Override
    public void start() {
        super.start();

        super.getLocation().getBlock().setType(Material.CRIMSON_STEM);
        Bukkit.getPluginManager().registerEvents(this, super.getServices().getPlugin());
    }

    @Override
    public void remove() {
        super.getLocation().getBlock().setType(Material.AIR);
    }

    @Override
    public void stop() {
        HandlerList.unregisterAll(this);
        super.getLocation().getBlock().setType(Material.BEDROCK);
        super.stop();
    }

    @Override
    public void destroyCoreAnimation() {
        super.getLocation().getBlock().setType(Material.BEDROCK);
    }

    @Override
    public void hit(Player player) {
        super.hit(player);
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.MONITOR)
    public void entityDamage(BlockBreakEvent event) {
        super.hit(event.getPlayer());
        event.setCancelled(true);
    }
}