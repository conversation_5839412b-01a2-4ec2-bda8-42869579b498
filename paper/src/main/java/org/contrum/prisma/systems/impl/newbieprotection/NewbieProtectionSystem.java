package org.contrum.prisma.systems.impl.newbieprotection;

import lombok.Getter;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityPickupItemEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerMoveEvent;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.actionbar.bars.impl.placeholder.GlobalPlaceHolderActionBar;
import org.contrum.prisma.events.BlockDropEvent;
import org.contrum.prisma.events.KothStartCampingEvent;
import org.contrum.prisma.events.PlayerUseAbilityEvent;
import org.contrum.prisma.events.TreasureChestOpenEvent;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.systems.SystemListener;
import org.contrum.prisma.utils.PlayerUtils;
import org.contrum.prisma.utils.WorldGuardUtils;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;
import java.util.List;
import java.util.UUID;

@Getter
public class NewbieProtectionSystem extends SystemListener implements Listener {
    private final PaperServices services;

    //Configs
    private List<String> disabledArmors = super.getConfig().getStringList("DISABLED_ARMORS");
    private List<String> disabledRegions = super.getConfig().getStringList("DISABLED_REGIONS");
    private List<String> disabledTreasureChests = super.getConfig().getStringList("DISABLED_TREASURE_CHESTS");
    private Duration duration = TimeUtils.parseDuration(super.getConfig().getString("DURATION", "30m"));

    public NewbieProtectionSystem(PaperServices services) {
        super(services);

        this.services = services;
        services.getActionBarService().registerGlobalActionBar(
                new GlobalPlaceHolderActionBar(
                        services.getTranslator(),
                        "NewbieProtection",
                        13,
                        (p) -> {
                            ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(p.getUniqueId(), ProfilePaperMetadata.class);
                            return this.hasProtection(metadata);
                        },
                        "NEWBIE_PROTECTION.ACTIONBAR")
                        .setFunction((p) -> {
                            ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(p.getUniqueId(), ProfilePaperMetadata.class);
                            return new Object[]{this.getRemaining(metadata)};
                        })
        );
    }

    @Override
    protected void onReload() {
        disabledArmors = super.getConfig().getStringList("DISABLED_ARMORS");
        disabledRegions = super.getConfig().getStringList("DISABLED_REGIONS");
        disabledTreasureChests = super.getConfig().getStringList("DISABLED_TREASURE_CHESTS");
        duration = TimeUtils.parseDuration(super.getConfig().getString("DURATION", "30m"));
    }

    @Override
    public String getConfigID() {
        return "NEWBIE_PROTECTION";
    }


    public boolean hasProtection(ProfilePaperMetadata metadata) {
        return metadata.hasTimer(NewbieProtectionTimer.class);
    }

    public Duration getRemaining(ProfilePaperMetadata metadata) {
        return Duration.ofMillis(metadata.getTimer(NewbieProtectionTimer.class).getRemainingTime());
    }

    public boolean isAllowedToGoTo(ProfilePaperMetadata metadata, Location location) {
        return !hasProtection(metadata) || WorldGuardUtils.getRegionsName(location).stream().noneMatch(this.disabledRegions::contains);
    }

    public void disableProtection(UUID uuid) {
        Profile profile = services.getProfileService().getOrLoadProfile(uuid);
    }

    public boolean disableProtection(Profile profile) {
        ProfilePaperMetadata metadata = profile.getServerMetadata(services.getProfileService(), ProfilePaperMetadata.class);

        if (this.hasProtection(metadata)) {
            metadata.removeTimer(NewbieProtectionTimer.class);
            services.getProfileService().saveIfOffline(profile);
            return true;
        }

        return false;
    }

    public boolean enableProtection(Player player) {
        Profile profile = services.getProfileService().getOrLoadProfile(player.getUniqueId());
        ProfilePaperMetadata metadata = profile.getServerMetadata(services.getProfileService(), ProfilePaperMetadata.class);

        if (this.hasProtection(metadata)) {
            return false;
        }

        metadata.addTimer(new NewbieProtectionTimer(services, duration, player.getUniqueId()));
        WorldUtils.playSound(Sound.ENTITY_PLAYER_LEVELUP, player);
        services.getTranslator().send(player, "NEWBIE_PROTECTION.ENABLED", duration);
        return true;
    }

    @EventHandler
    public void playerJoinEvent(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        if (!player.hasPlayedBefore()) {
            TaskUtil.runLater(services.getPlugin(), () -> {
                this.enableProtection(player);
            }, 20L*3);
        }
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGH)
    public void customBlockBreak(BlockDropEvent event) {
        if (event.getArmor() != null && this.disabledArmors.contains(event.getArmor().getName())) {
            ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(event.getPlayer().getUniqueId(), ProfilePaperMetadata.class);

            if (this.hasProtection(metadata)) {
                services.getTranslator().send(event.getPlayer(), "NEWBIE_PROTECTION.MINE_BREAK_DISABLED");
                event.setCancelled(true);
            }
        }
    }

    @EventHandler
    public void kothCamp(KothStartCampingEvent event) {
        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(event.getPlayer().getUniqueId(), ProfilePaperMetadata.class);
        if (this.hasProtection(metadata)) {
            PlayerUtils.executeActionWithCooldown(metadata, "PVP_TIMER_MESSAGE_KOTH", Duration.ofSeconds(5), () -> {
                        services.getTranslator().send(event.getPlayer(), "NEWBIE_PROTECTION.KOTH_DISABLED");
                    });
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void useAbility(PlayerUseAbilityEvent event) {
        Player player = event.getPlayer();
        if (PlayerUtils.isInDuel(player)) return;

        ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
        if (this.hasProtection(metadata)) {
            services.getTranslator().send(player, "NEWBIE_PROTECTION.ABILITY_USE_DISABLED");
            WorldUtils.playNoSound(player);
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void treasureChestOpen(TreasureChestOpenEvent event) {
        if (disabledTreasureChests.contains(event.getChest().getChestType().getId())) {
            Player player = event.getPlayer();
            ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
            if (this.hasProtection(metadata)) {
                services.getTranslator().send(player, "NEWBIE_PROTECTION.TREASURE_CHEST_DISABLED");
                WorldUtils.playNoSound(player);
                event.setCancelled(true);
            }
        }
    }

    @EventHandler
    public void playerMoveEvent(PlayerMoveEvent event) {
        Player player = event.getPlayer();
        TaskUtil.runAsync(services.getPlugin(), () -> {
            ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
            if (event.hasExplicitlyChangedPosition() && !isAllowedToGoTo(metadata, event.getTo())) {
                if (player.isGliding()) {
                    player.setGliding(false);
                }

                services.getTranslator().send(player, "NEWBIE_PROTECTION.MOVE_DISABLED");
                player.teleportAsync(event.getFrom());
            }
        });
    }

    @EventHandler
    public void playerPickupItem(EntityPickupItemEvent event) {
        if (event.getEntity() instanceof Player player) {
            UUID thrower = event.getItem().getThrower();
            if (thrower != null && thrower.equals(player.getUniqueId())) return;

            ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);
            if (this.hasProtection(metadata)) {
                PlayerUtils.executeActionWithCooldown(metadata, "PVP_TIMER_MESSAGE_PICKUP", Duration.ofSeconds(5), () -> {
                    services.getTranslator().send(player, "NEWBIE_PROTECTION.PICKUP_DISABLED");
                });

                event.setCancelled(true);
            }
        }
    }

    @EventHandler(ignoreCancelled = true, priority = EventPriority.HIGH)
    public void entityDamageEvent(EntityDamageEvent event) {
        if (event.getEntity() instanceof Player player) {
            if (PlayerUtils.isInDuel(player)) return;

            ProfilePaperMetadata metadata = services.getProfileService().getServerMetadata(player.getUniqueId(), ProfilePaperMetadata.class);

            if (event instanceof EntityDamageByEntityEvent damageEvent && damageEvent.getDamager() instanceof Player damager) {
                ProfilePaperMetadata damagerMetadata = services.getProfileService().getServerMetadata(damager.getUniqueId(), ProfilePaperMetadata.class);
                if (this.hasProtection(damagerMetadata)) {
                    services.getTranslator().send(damager, "NEWBIE_PROTECTION.ATTACK_DISABLED");
                    event.setCancelled(true);
                } else if (this.hasProtection(metadata)) {
                    services.getTranslator().send(damager, "NEWBIE_PROTECTION.PLAYER_HAS_PROTECTION");
                    event.setCancelled(true);
                }
                return;
            }

            if (this.hasProtection(metadata))
                event.setCancelled(true);
        }
    }
}
