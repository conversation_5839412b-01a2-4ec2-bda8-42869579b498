package org.contrum.prisma.systems.impl.battlepass.quests;

import lombok.Getter;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.config.reward.ConfigReward;

import java.util.ArrayList;
import java.util.List;

@Getter
public class BattlePassQuestReward {
    private final List<String> rewardsDisplay;
    private final List<ConfigReward> rewards = new ArrayList<>();

    public BattlePassQuestReward(PaperServices services, ConfigurationSection section) {
        this.rewardsDisplay = section.getStringList("DISPLAY");
        ConfigurationSection rewardsSection = section.getConfigurationSection("REWARDS");
        if (rewardsSection == null) return;

        for (String key : rewardsSection.getKeys(false)) {
            ConfigurationSection s = rewardsSection.getConfigurationSection(key);
            if (s != null)
                rewards.add(new ConfigReward(services, s));
        }
    }

    public void giveRewards(PaperServices services, Player player) {
        for (ConfigReward reward : rewards) {
            reward.give(services, player);
        }
    }
}
