package org.contrum.prisma.systems.impl.newbieprotection;

import org.bson.Document;
import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.timer.impl.PlayerTimer;

import java.time.Duration;
import java.util.UUID;

public class NewbieProtectionTimer extends PlayerTimer {

    public NewbieProtectionTimer(PaperServices services, Document document) {
        super(services, document);
    }

    public NewbieProtectionTimer(PaperServices services, Duration duration, UUID uuid) {
        super(services, duration, uuid);
    }

    @Override
    public String getName() {
        return "NewbieProtection";
    }

    @Override
    protected void onExpire(UUID uuid) {
        Player player = Bukkit.getPlayer(uuid);
        if (player != null) {
            WorldUtils.playSound(Sound.ENTITY_LLAMA_SPIT, player, 5, 0);
            super.getServices().getTranslator().send(player, "NEWBIE_PROTECTION.EXPIRED");
        }
    }

    @Override
    protected void onTick(UUID uuid) {

    }

    @Override
    public boolean tickOnlyWhileOnline() {
        return true;
    }

    @Override
    public void onRemove(UUID uuid) {

    }

    @Override
    public String getDisplayName() {
        return "NewbieProtection";
    }
}
