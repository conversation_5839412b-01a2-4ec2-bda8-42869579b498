package org.contrum.prisma.systems.impl;

import me.ulrich.clans.data.ClanData;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.systems.SystemListener;
import org.contrum.prisma.utils.UClansUtils;
import org.contrum.tritosa.Translator;

import java.util.*;

public class ClanNerfSystem extends SystemListener implements Listener {

    private int max_nearby_clan_members = this.getConfig().getInt("MAX_NEARBY_CLAN_MEMBERS", 5);
    private double clan_members_range = this.getConfig().getInt("CLAN_MEMBERS_RANGE", 10);
    private double clans_nearby_range = this.getConfig().getInt("CLANS_NEARBY_RANGE", 15);
    private double nerf_damage_reduce_percentage = this.getConfig().getInt("NERF_DAMAGE_REDUCE_PERCENTAGE", 20);
    private double nerf_resistance_reduce_percentage = this.getConfig().getInt("NERF_RESISTANCE_REDUCE_PERCENTAGE", 10);

    private final Translator translator;

    private final Set<UUID> nerfedClans = Collections.synchronizedSet(new HashSet<>());

    public ClanNerfSystem(PaperServices services) {
        super(services);

        this.translator = services.getTranslator();

        TaskUtil.runTimer(services.getPlugin(), () -> {
            if (!this.isEnabled()) return;

            Set<UUID> checkedClans = new HashSet<>();
            Set<UUID> newNerfedClans = new HashSet<>();
            for (Player player : Bukkit.getOnlinePlayers()) {
                ClanData clan = UClansUtils.getClan(services, player);
                if (clan == null) continue;

                if (nerfedClans.contains(clan.getId()) || checkedClans.contains(clan.getId()))
                    continue;

                List<Player> nearbyClanPlayers = UClansUtils.getNearbyClanPlayers(services, player, clan_members_range);
                if (nearbyClanPlayers.size() > max_nearby_clan_members) {
                    checkedClans.add(clan.getId());

                    //find another nearby clans :D
                    Collection<Player> players = player.getLocation().getNearbyPlayers(clans_nearby_range);
                    for (Player otherPlayer : players) {
                        ClanData otherClan = UClansUtils.getClan(services, player);
                        if (otherClan == null || otherClan.equals(clan)) continue;

                        //big nearby clan found, shouldn't nerf ^^
                        if (UClansUtils.getNearbyClanPlayers(services, otherPlayer, clan_members_range).size() > max_nearby_clan_members) {
                            continue;
                        }
                    }

                    //No big nearby clans found, clan should be nerfed
                    newNerfedClans.add(clan.getId());
                }
            }

            //Update nerf list ^^
            for (UUID nerfedClan : nerfedClans) {
                if (!newNerfedClans.contains(nerfedClan)) setNerf(nerfedClan, false);
            }

            for (UUID uuid : newNerfedClans) {
                this.setNerf(uuid, true);
            }
        }, 0L, 40L);
    }

    @Override
    public boolean isEnabled() {
        return super.isEnabled() && getServices().isUClansWorking();
    }

    @Override
    protected void onReload() {
        max_nearby_clan_members = this.getConfig().getInt("MAX_NEARBY_CLAN_MEMBERS", 5);
        clan_members_range = this.getConfig().getInt("CLAN_MEMBERS_RANGE", 10);
        clans_nearby_range = this.getConfig().getInt("CLANS_NEARBY_RANGE", 15);
        nerf_damage_reduce_percentage = this.getConfig().getInt("NERF_DAMAGE_REDUCE_PERCENTAGE", 20);
        nerf_resistance_reduce_percentage = this.getConfig().getInt("NERF_RESISTANCE_REDUCE_PERCENTAGE", 10);
    }

    @Override
    public String getConfigID() {
        return "CLANS_NERF";
    }

    public boolean isNerfed(ClanData data) {
        return this.nerfedClans.contains(data.getId());
    }

    public void setNerf(UUID uuid, boolean nerf) {
        if (!nerf) {
            if (this.nerfedClans.remove(uuid)) {
                Optional<ClanData> optionalClan = this.getServices().getUClans().getClanAPI().getClan(uuid);
                if (optionalClan.isEmpty()) return;

                ClanData clan = optionalClan.get();
                clan.getOnlineMembers().forEach(u -> {
                    Player player = Bukkit.getPlayer(u);
                    if (player == null) return;

                    translator.send(player, "CLAN.NERF_REMOVE");
                });
            }
        } else {
            if (this.nerfedClans.add(uuid)) {
                Optional<ClanData> optionalClan = this.getServices().getUClans().getClanAPI().getClan(uuid);
                if (optionalClan.isEmpty()) return;

                ClanData clan = optionalClan.get();
                clan.getOnlineMembers().forEach(u -> {
                    Player player = Bukkit.getPlayer(u);
                    if (player == null) return;

                    translator.send(player, "CLAN.NERF");
                });
            }
        }
    }

    @EventHandler(priority = EventPriority.HIGH)
    public void damage(EntityDamageByEntityEvent event) {
        if (event.getDamager() instanceof Player damager && event.getEntity() instanceof Player) {
            ClanData clan = UClansUtils.getClan(getServices(), damager);
            if (clan == null) return;

            if (this.isNerfed(clan)) {
                double newDamage = event.getDamage() - (event.getDamage() * (nerf_damage_reduce_percentage / 100));
                event.setDamage(newDamage);
                return;
            }
        }

        if (event.getDamager() instanceof Player && event.getEntity() instanceof Player player) {
            ClanData clan = UClansUtils.getClan(getServices(), player);
            if (clan == null) return;

            if (this.isNerfed(clan)) {
                double newDamage = event.getDamage() + (event.getDamage() * (nerf_resistance_reduce_percentage / 100));
                event.setDamage(newDamage);
                return;
            }
        }
    }
}
