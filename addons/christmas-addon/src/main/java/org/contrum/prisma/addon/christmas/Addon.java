package org.contrum.prisma.addon.christmas;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.PaperCommandManager;
import lombok.Getter;
import org.bukkit.plugin.java.JavaPlugin;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.addon.christmas.commands.ChristmasEventCommand;
import org.contrum.prisma.addon.christmas.commands.EquipoCommand;
import org.contrum.prisma.addon.christmas.event.SpecialEventServiceSystem;
import org.contrum.prisma.customitems.ability.Ability;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.profile.addon.PaperAddon;
import org.contrum.prisma.systems.System;
import org.contrum.prisma.utils.config.ConfigFile;
import org.contrum.tritosa.Translator;

import java.io.IOException;
import java.util.HashSet;
import java.util.Set;

@Getter
public class Addon extends PaperAddon {

    private JavaPlugin plugin;
    private Translator translator;
    private PaperServices services;
    private ProfileService profileService;
    private PaperCommandManager commandManager;

    private ConfigFile mainConfig;
    private ConfigFile itemsConfig;

    private SpecialEventServiceSystem eventServiceSystem;

    private final Set<BaseCommand> commands = new HashSet<>();

    @Override
    public void onEnable() {
        this.services = PaperAddon.getMainServices();
        this.plugin = services.getPlugin();
        this.translator = services.getTranslator();
        this.profileService = services.getProfileService();
        this.commandManager = services.getCommandManager();

        try {
            services.registerLanguageExtension(this, "English", this.getDataFolder().toPath().resolve("lang_en.yml"));
        } catch (IOException e) {
            e.printStackTrace();
        }

        this.mainConfig = new ConfigFile(this, "config.yml");
        this.itemsConfig = new ConfigFile(this, "items.yml");
        services.getScoreboardService().registerPluginBoards(this);

        this.eventServiceSystem = new SpecialEventServiceSystem(this);

        this.registerCommands();

        services.getAddonService().registerAddon(this);
        this.setLoaded(true);
    }

    public void registerCommands() {
        commandManager.registerDependency(SpecialEventServiceSystem.class, eventServiceSystem);
        commands.add(new ChristmasEventCommand());
        commands.add(new EquipoCommand());

        commands.forEach(commandManager::registerCommand);
    }

    @Override
    public void onDisable() {
        commands.forEach(commandManager::unregisterCommand);

        eventServiceSystem.unload();

        this.setLoaded(false);
    }

    @Override
    public void onReload() {
        this.mainConfig = new ConfigFile(this, "config.yml");
        this.itemsConfig = new ConfigFile(this, "items.yml");
    }


    @Override
    public String getAddonName() {
        return "Christmas";
    }

    @Override
    public ProfileService getProfileService() {
        return profileService;
    }
}
