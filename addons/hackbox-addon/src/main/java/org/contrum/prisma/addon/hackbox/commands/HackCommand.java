package org.contrum.prisma.addon.hackbox.commands;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import org.bukkit.entity.Player;
import org.contrum.prisma.PaperServices;
import org.contrum.prisma.addon.hackbox.Addon;
import org.contrum.prisma.addon.hackbox.hacks.HacksService;
import org.contrum.prisma.addon.hackbox.hacks.menu.HacksSettingsMenu;

@CommandAlias("hacks|hack")
public class HackCommand extends BaseCommand {

    @Dependency
    private Addon addon;

    @Default
    public void open(Player player) {
        new HacksSettingsMenu(addon).open(player);
    }
}