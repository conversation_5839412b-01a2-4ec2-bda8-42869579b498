package org.contrum.prisma.addon.hub.profile;

import lombok.Getter;
import lombok.NoArgsConstructor;
import org.bson.Document;
import org.contrum.prisma.addon.hub.minigames.statistics.MiniGameStatistics;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.metadata.ProfileMetadata;

import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
public class ProfileHubMetadata extends ProfileMetadata {

    private final Map<String, MiniGameStatistics> miniGameStatisticsMap = new HashMap<>();

    public ProfileHubMetadata(Profile profile) {
        super(profile);
    }

    public ProfileHubMetadata(Profile profile, Document document) {
        super(profile);
        Document statistics = document.get("statistics", Document.class);
        for (Map.Entry<String, Object> entry : statistics.entrySet()) {
            Document serialized = statistics.get(entry.getKey(), Document.class);
            miniGameStatisticsMap.put(entry.getKey(), new MiniGameStatistics(serialized));
        }
    }

    public MiniGameStatistics getMiniGameStatistic(String id) {
        return miniGameStatisticsMap.computeIfAbsent(id, f -> new MiniGameStatistics());
    }

    @Override
    public Document serialize() {
        return new Document()
                .append("statistics", new Document(miniGameStatisticsMap.entrySet().stream().collect(
                        Collectors.toMap(
                                Map.Entry::getKey,
                                entry -> entry.getValue().serialize()
                        ))));
    }
}
