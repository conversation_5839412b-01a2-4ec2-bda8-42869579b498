package org.contrum.prisma.addon.hub.minigames.impl.dropper;

import lombok.Getter;
import org.bukkit.*;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;
import org.bukkit.util.BoundingBox;
import org.bukkit.util.Vector;
import org.contrum.prisma.addon.hub.Addon;
import org.contrum.prisma.addon.hub.minigames.MiniGame;
import org.contrum.prisma.addon.hub.minigames.MiniGameMatch;
import org.contrum.prisma.addon.hub.minigames.statistics.MiniGameStatistics;
import org.contrum.prisma.utils.ItemBuilder1_20;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.time.Duration;
import java.time.Instant;
import java.util.List;

@Getter
public class DropperMatch extends MiniGameMatch implements Listener {

    public static final String WIN_STREAK = "WINSTREAK";
    public static final String BEST_TIME = "BEST-TIME";
    public static final String TOTAL_WINS = "TOTAL-WINS";
    public static final String TOTAL_ATTEMPTS = "TOTAL-ATTEMPTS";
    public static final String COMPLETION_RATE = "COMPLETION-RATE";
    public static final String BEST_WIN_STREAK = "BEST-WINSTREAK";

    private final DropperGame game;
    private Instant startTime;
    private boolean gameActive = false;
    private int attemptNumber = 1;

    private BukkitTask gameTask;

    public DropperMatch(Addon addon, MiniGame game, Player player) {
        super(addon, game, player);
        this.game = (DropperGame) game;
        this.startTime = Instant.now();
    }

    @Override
    protected void onStart() {
        player.setGameMode(GameMode.SURVIVAL);
        player.setFlying(false);
        player.setAllowFlight(false);
        player.getInventory().clear();

        Bukkit.getPluginManager().registerEvents(this, addon);

        ItemStack arrow = new ItemBuilder1_20(Material.ARROW)
                .name(addon.getTranslator().getAsText(player, "MINIGAMES.DROPPER.RESET_ITEM.NAME"))
                .lore(addon.getTranslator().getListString(player, "MINIGAMES.DROPPER.RESET_ITEM.LORE"))
                .build();
        ItemStack back = new ItemBuilder1_20(Material.RED_BED)
                .name(addon.getTranslator().getAsText(player, "MINIGAMES.DROPPER.LEAVE_ITEM.NAME"))
                .lore(addon.getTranslator().getListString(player, "MINIGAMES.DROPPER.LEAVE_ITEM.LORE"))
                .build();

        player.getInventory().setItem(0, arrow);
        player.getInventory().setItem(8, back);

        MiniGameStatistics stats = getPlayerStatistics();
        long currentStreak = stats.getOrDefault(WIN_STREAK, 0L);
        long bestTime = stats.getOrDefault(BEST_TIME, 0L);

        LocalPlaceholders startPlaceholders = LocalPlaceholders.builder()
                .add("<streak>", String.valueOf(currentStreak))
                .add("<best_time>", bestTime > 0 ? formatTime(Duration.ofMillis(bestTime)) : "N/A")
                .add("<attempt>", String.valueOf(attemptNumber));

        addon.getTranslator().send(player, "MINIGAMES.DROPPER.GAME_STARTED", startPlaceholders);

        player.sendTitle(
                addon.getTranslator().getAsText(player, "MINIGAMES.DROPPER.START_TITLE"),
                addon.getTranslator().getAsText(player, "MINIGAMES.DROPPER.START_SUBTITLE", startPlaceholders),
                1, 3, 1
        );

        player.playSound(player.getLocation(), Sound.BLOCK_NOTE_BLOCK_PLING, 1.0f, 1.2f);

        gameActive = true;
        startGameTimer();
    }

    private void startGameTimer() {
        if (gameTask != null) {
            gameTask.cancel();
        }

        gameTask = new BukkitRunnable() {
            private Location previousLocation = null;
            private int stuckCounter = 0;

            @Override
            public void run() {
                if (!gameActive) {
                    this.cancel();
                    return;
                }

                Player player = getPlayer();
                Location currentLocation = player.getLocation();
                List<Location> obstacleLocations = game.getGeneratedLocations().get(player.getUniqueId());

                if (obstacleLocations != null) {
                    boolean collision = isPlayerCollidingWithAnyObstacle(player, obstacleLocations);

                    boolean isStuck = false;
                    if (previousLocation != null) {
                        double distance = currentLocation.distance(previousLocation);
                        if (distance < 0.05) {
                            stuckCounter++;
                            if (stuckCounter >= 2) {
                                Material blockType = currentLocation.getBlock().getType();
                                if (!blockType.name().contains("WATER") && !player.isFlying()) {
                                    isStuck = true;
                                }
                            }
                        } else {
                            stuckCounter = 0;
                        }
                    }

                    if (collision || isStuck) {
                        if (gameActive && isPlayerCollidingWithAnyObstacle(player, obstacleLocations)) {
                            getGame().stopMatch(DropperMatch.this, MatchEndReason.LOSE);
                            teleport();
                        }
                    } else {
                        if (currentLocation.getBlock().getType().name().contains("WATER")) {
                            getGame().stopMatch(DropperMatch.this, MatchEndReason.WIN);
                            teleport();
                        }
                    }

                    previousLocation = currentLocation.clone();
                } else {
                    reset();
                }
            }
        }.runTaskTimer(getAddon(), 0L, 2L); // Every 2 ticks
    }


    private boolean isPlayerCollidingWithAnyObstacle(Player player, List<Location> obstacles) {
        BoundingBox playerBox = player.getBoundingBox();

        for (Location obs : obstacles) {
            BoundingBox blockBox = BoundingBox.of(
                    obs.getBlock()
            ).expand(0, 0, 0, 0, 0.02, 0);

            if (playerBox.overlaps(blockBox)) {
                player.getWorld().spawnParticle(org.bukkit.Particle.DUST,
                        obs.clone().add(0.5, 0.5, 0.5), 10,
                        new org.bukkit.Particle.DustOptions(org.bukkit.Color.RED, 1.0f));
                return true;
            }
        }
        return false;
    }

    @Override
    protected void onStop(MatchEndReason reason) {
        HandlerList.unregisterAll(this);

        MiniGameStatistics playerStatistics = super.getPlayerStatistics();

        long totalAttempts = playerStatistics.getOrDefault(TOTAL_ATTEMPTS, 0L);
        playerStatistics.set(TOTAL_ATTEMPTS, totalAttempts + 1);

        if (reason == MatchEndReason.WIN) {
            handleWin(playerStatistics);
        } else if (reason == MatchEndReason.LOSE) {
            handleLose(playerStatistics);
        } else if (reason == MatchEndReason.QUIT) {
            handleQuit();
        }

        long totalWins = playerStatistics.getOrDefault(TOTAL_WINS, 0L);
        double completionRate = totalAttempts > 0 ? (double) totalWins / totalAttempts * 100 : 0;
        playerStatistics.set(COMPLETION_RATE, (long) completionRate);

        if (gameTask != null) {
            gameTask.cancel();
        }

        reset();
    }

    private void handleWin(MiniGameStatistics stats) {
        Duration completionTime = getElapsedTime();

        stats.increase(WIN_STREAK, 1);

        if (stats.getOrDefault(WIN_STREAK, 0L) > stats.getOrDefault(BEST_WIN_STREAK, 0L)) {
            stats.set(BEST_WIN_STREAK, stats.getOrDefault(WIN_STREAK, 0L));
        }

        long totalWins = stats.getOrDefault(TOTAL_WINS, 0L);
        stats.set(TOTAL_WINS, totalWins + 1);

        long bestTime = stats.getOrDefault(BEST_TIME, Long.MAX_VALUE);
        boolean isNewRecord = completionTime.toMillis() < bestTime;

        if (isNewRecord) {
            stats.set(BEST_TIME, completionTime.toMillis());
        }

        long currentStreak = stats.getOrDefault(WIN_STREAK, 0L);
        LocalPlaceholders winPlaceholders = LocalPlaceholders.builder()
                .add("<time>", formatTime(completionTime))
                .add("<streak>", String.valueOf(currentStreak))
                .add("<attempt>", String.valueOf(attemptNumber));

        if (isNewRecord) {
            WorldUtils.playSound(Sound.UI_TOAST_CHALLENGE_COMPLETE, player);
            addon.getTranslator().send(player, "MINIGAMES.DROPPER.NEW_RECORD", winPlaceholders);
            player.sendTitle(
                    addon.getTranslator().getAsText(player, "MINIGAMES.DROPPER.NEW_RECORD_TITLE", winPlaceholders),
                    addon.getTranslator().getAsText(player, "MINIGAMES.DROPPER.NEW_RECORD_SUBTITLE", winPlaceholders),
                    1, 3, 1
            );
        } else {
            WorldUtils.playSound(Sound.ENTITY_PLAYER_LEVELUP, player);
            addon.getTranslator().send(player, "MINIGAMES.DROPPER.WIN", winPlaceholders);
            player.sendTitle(
                    addon.getTranslator().getAsText(player, "MINIGAMES.DROPPER.WIN_TITLE"),
                    addon.getTranslator().getAsText(player, "MINIGAMES.DROPPER.WIN_SUBTITLE", winPlaceholders),
                    1, 3, 1
            );
        }

        if (currentStreak >= 5 && currentStreak % 5 == 0) {
            LocalPlaceholders streakPlaceholders = LocalPlaceholders.builder()
                    .add("<streak>", String.valueOf(currentStreak));
            addon.getTranslator().send(player, "MINIGAMES.DROPPER.STREAK_MILESTONE", streakPlaceholders);
        }
    }

    private void handleLose(MiniGameStatistics stats) {
        Duration survivalTime = getElapsedTime();

        long previousStreak = stats.getOrDefault(WIN_STREAK, 0L);
        stats.set(WIN_STREAK, 0L);

        LocalPlaceholders losePlaceholders = LocalPlaceholders.builder()
                .add("<time>", formatTime(survivalTime))
                .add("<lost_streak>", String.valueOf(previousStreak))
                .add("<attempt>", String.valueOf(attemptNumber));

        if (previousStreak > 0) {
            addon.getTranslator().send(player, "MINIGAMES.DROPPER.LOSE_WITH_STREAK", losePlaceholders);
        } else {
            addon.getTranslator().send(player, "MINIGAMES.DROPPER.LOSE", losePlaceholders);
        }

        player.sendTitle(
                addon.getTranslator().getAsText(player, "MINIGAMES.DROPPER.LOSE_TITLE"),
                addon.getTranslator().getAsText(player, "MINIGAMES.DROPPER.LOSE_SUBTITLE", losePlaceholders),
                1, 3, 1
        );

        addon.getTranslator().send(player, "MINIGAMES.DROPPER.TRY_AGAIN");
    }

    private void handleQuit() {
        addon.getTranslator().send(player, "MINIGAMES.DROPPER.QUIT");

        player.playSound(player.getLocation(), Sound.UI_BUTTON_CLICK, 1.0f, 1.0f);
    }

    public void teleport() {
        WorldUtils.playUiClickSound(getPlayer());
        player.teleport(game.getStartLocation());
    }

    @EventHandler(priority = EventPriority.HIGHEST)
    public void interact(PlayerInteractEvent event) {
        Player player = event.getPlayer();

        if (player.equals(super.getPlayer())) {
            event.setCancelled(true);

            ItemStack item = event.getItem();

            if (event.getAction().isRightClick() && item != null) {
                if (item.getType().equals(Material.ARROW)) {
                    WorldUtils.playUiClickSound(player);
                    Duration currentTime = getElapsedTime();
                    LocalPlaceholders resetPlaceholders = LocalPlaceholders.builder()
                            .add("<time>", formatTime(currentTime))
                            .add("<attempt>", String.valueOf(attemptNumber + 1));

                    addon.getTranslator().send(player, "MINIGAMES.DROPPER.MANUAL_RESET", resetPlaceholders);
                    attemptNumber++;
                    reset();
                } else if (item.getType().equals(Material.RED_BED)) {
                    WorldUtils.playUiClickSound(player);
                    this.getGame().stopMatch(this, MatchEndReason.QUIT);
                }
            }
        }
    }

    public void reset() {
        this.teleport();
        randomizeDropper();
        startTime = Instant.now();
    }

    public void randomizeDropper() {
        DropperObstacleGenerator generator = new DropperObstacleGenerator();

        generator.clearArea(player, game.getGameRegion());

        game.getGeneratedLocations().put(player.getUniqueId(), generator.generateDropperObstacles(
                player,
                game.getGameRegion()));

        if (attemptNumber > 1) {
            addon.getTranslator().send(player, "MINIGAMES.DROPPER.NEW_LAYOUT");
        }
    }

    public Duration getElapsedTime() {
        return Duration.between(startTime, Instant.now());
    }

    private String formatTime(Duration duration) {
        long totalMillis = duration.toMillis();
        long minutes = totalMillis / 60000;
        long seconds = (totalMillis % 60000) / 1000;
        long millis = totalMillis % 1000;

        if (minutes > 0) {
            return String.format("%dm %d.%03ds", minutes, seconds, millis);
        } else {
            return String.format("%d.%03ds", seconds, millis);
        }
    }

    public long getCurrentStreak() {
        return getPlayerStatistics().getOrDefault(WIN_STREAK, 0L);
    }

    public Duration getBestTime() {
        long bestTimeMillis = getPlayerStatistics().getOrDefault(BEST_TIME, 0L);
        return bestTimeMillis > 0 ? Duration.ofMillis(bestTimeMillis) : Duration.ZERO;
    }

    public double getCompletionRate() {
        return getPlayerStatistics().getOrDefault(COMPLETION_RATE, 0);
    }
}