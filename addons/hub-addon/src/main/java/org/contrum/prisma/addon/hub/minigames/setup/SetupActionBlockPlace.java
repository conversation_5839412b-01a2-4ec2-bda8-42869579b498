package org.contrum.prisma.addon.hub.minigames.setup;

import lombok.Getter;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.HandlerList;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.inventory.ItemStack;
import org.contrum.prisma.addon.hub.minigames.MiniGame;
import org.contrum.prisma.utils.WorldUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.BiConsumer;

public class SetupActionBlockPlace extends SetupAction implements Listener {

    private final List<PlaceableBlock> blocks;

    public SetupActionBlockPlace(Collection<PlaceableBlock> blocks, MiniGame miniGame, String message, SetupAction nextAction) {
        super(miniGame, message, nextAction);

        this.blocks = new ArrayList<>(blocks);
    }

    @Override
    protected void onStart(Player player) {
        Bukkit.getPluginManager().registerEvents(this, super.getMiniGame().getAddon());

        //Apply inventory
        player.getInventory().clear();
        for (PlaceableBlock block : blocks) {
            player.getInventory().addItem(new ItemStack(block.getItem()));
        }
    }

    @Override
    public void onStop() {
        HandlerList.unregisterAll(this);
    }

    @EventHandler(priority = EventPriority.HIGHEST)
    public void blockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();

        if (player.equals(super.getPlayer())) {
            event.setCancelled(true);

            ItemStack item = event.getItemInHand();
            for (PlaceableBlock block : blocks) {
                if (block.getItem().isSimilar(item)) {
                    player.getInventory().setItemInMainHand(null);
                    WorldUtils.playSuccessSound(player);
                    block.getOnPlace().accept(player, event.getBlock());

                    blocks.remove(block);
                    if (blocks.isEmpty()) {
                        super.finish();
                    }
                    break;
                }
            }
        }
    }

    @Getter
    public static class PlaceableBlock {

        private final ItemStack item;
        private final Material material;
        private final BiConsumer<Player, Block> onPlace;

        public PlaceableBlock(ItemStack item, BiConsumer<Player, Block> onPlace) {
            this.item = item;
            this.material = item.getType();
            this.onPlace = onPlace;
        }
    }
}
