package org.contrum.prisma.addon.hub.hotbar.impl;

import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.Vector;
import org.contrum.prisma.addon.hub.hotbar.HotBar;
import org.contrum.prisma.addon.hub.hotbar.HotBarButton;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.config.reward.ConfigReward;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.function.Function;

public class ConfigurationHotBar extends HotBar {

    private ConfigurationSection section;

    public ConfigurationHotBar(ConfigurationSection section) {
        this.section = section;
    }

    @Override
    public Map<Integer, HotBarButton> getButtonsInternal(Player player) {
        Map<Integer, HotBarButton> map = new HashMap<>();

        for (String key : section.getKeys(false)) {
            ConfigurationSection keySection = section.getConfigurationSection(key);
            int slot = Integer.parseInt(key);

            ConfigurationSection itemSection = keySection.getConfigurationSection("ITEM");
            if (itemSection == null) continue;

            ItemStack item = new ConfigReward(null, itemSection).getItemStack();

            //Get actions
            List<BiConsumer<Player, Boolean>> actions = new ArrayList<>();
            ConfigurationSection actionSection = keySection.getConfigurationSection("ACTIONS");
            if (actionSection != null) {
                for (String type : actionSection.getKeys(false)) {
                    Function<ConfigurationSection, BiConsumer<Player, Boolean>> function = ACTIONS_TYPE.get(type);
                    if (function == null) {
                        Bukkit.getLogger().warning("Action type " + type + " doesn't exist!");
                        continue;
                    }

                    actions.add(function.apply(actionSection.getConfigurationSection(type)));
                }
            }

            //Create button
            HotBarButton button = new HotBarButton(item, actions);
            map.put(slot, button);
        }

        return map;
    }

    public void reload(ConfigurationSection section) {
        this.section = section;
        this.setDirty();
    }

    public final static Map<String, Function<ConfigurationSection, BiConsumer<Player, Boolean>>> ACTIONS_TYPE = new HashMap<>();

    static {
        ACTIONS_TYPE.put("COMMAND", (section) -> {
            String command = section.getString("COMMAND", "");
            boolean runAsConsole = section.getBoolean("RUN_AS_CONSOLE", false);

            boolean onlyRight = section.getBoolean("ONLY_RIGHT", false);
            boolean onlyLeft = section.getBoolean("ONLY_LEFT", false);
            return (player, rightClick) -> {
                if ((onlyRight && !rightClick) || (onlyLeft && rightClick)) return;

                String s = command.replaceAll("<player_name>", player.getName());
                if (runAsConsole) {
                    Bukkit.dispatchCommand(Bukkit.getConsoleSender(), s);
                } else {
                    player.performCommand(s);
                }
            };
        });

        ACTIONS_TYPE.put("VELOCITY", (section) -> {
            double force = section.getDouble("FORCE", 0);
            double y = section.getDouble("Y", -1);

            boolean onlyRight = section.getBoolean("ONLY_RIGHT", false);
            boolean onlyLeft = section.getBoolean("ONLY_LEFT", false);
            return (player, rightClick) -> {
                if ((onlyRight && !rightClick) || (onlyLeft && rightClick)) return;
                Vector vector = player.getLocation().getDirection().normalize().multiply(force);
                if (y != -1) vector.setY(y);
                player.setVelocity(vector);
            };
        });

        ACTIONS_TYPE.put("SOUND", (section) -> {
            Sound sound = Sound.valueOf(section.getString("SOUND", ""));
            float volume = (float) section.getDouble("VOLUME", 1);
            float pitch = (float) section.getDouble("PITCH", 1);
            boolean broadcast = section.getBoolean("BROADCAST", false);

            boolean onlyRight = section.getBoolean("ONLY_RIGHT", false);
            boolean onlyLeft = section.getBoolean("ONLY_LEFT", false);
            return (player, rightClick) -> {
                if ((onlyRight && !rightClick) || (onlyLeft && rightClick)) return;
                if (broadcast) {
                    WorldUtils.playSound(sound, volume, pitch);
                } else {
                    WorldUtils.playSound(sound, player, volume, pitch);
                }
            };
        });
    }
}
