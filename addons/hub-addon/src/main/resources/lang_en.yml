COMMANDS:
  BUILD_MODE:
    ENABLED: '&aYou have enabled your build mode.'
    DISABLED: '&cYou have disabled your build mode.'

    ENABLED_OTHER: '&aYou have enabled the build mode for &e<user_name>&a.'
    DISABLED_OTHER: '&cYou have disabled the build mode for &e<user_name>&a.'
MINIGAMES:
  ALREADY_IN_MATCH: '&cYa estás en una partida!'
  SETUP:
    PLAYER_LOCATION_ITEM:
      NAME: '&aClick derecho para aceptar!'
      LORE:
        - ''
        - '&7Usa click derecho en este item para guardar tu'
        - '&7posicion actual como localización.'
        - ''
    REGION_ACCEPT_ITEM:
      NAME: '&aClick derecho para aceptar!'
      LORE:
        - ''
        - '&7Usa click derecho en este item para enviar'
        - '&7la zona seleccionada con el hacha.'
        - ''
    REGION_AXE_ITEM:
      NAME: '&eSelector de zona'
      LORE:
        - ''
        - '&7Usa click izquierdo para seleccionar la primera posicion'
        - '&7y click derecho para la segunda.'
        - ''
    GLASS_ROW_NEXT:
      NAME: '&eSiguiente cristal'
      LORE:
        - ''
        - '&7Click para pasar al siguiente cristal'
        - ''
    GLASS_SELECTED: '&aHas seleccionado un cristal correctamente!'
    SETUP_MESSAGE: '&aUsa las herramientas para seleccionar &e<message>'
    REGION_INVALID_SELECTION: '&cSelecciona una zona primero!'
  SIMON_SAYS:
    LOSE: '&cTe has equivocado! Numero de aciertos: &e<amount>'
    NEW_RECORD: '&b&lsɪᴍᴏɴ ᴅɪᴄᴇ &r&7➜&r&a Has alcanzado un nuevo record de &e<amount>
      aciertos&a!'
    START_TITTLE: ''
    START_SUBTITLE: '&c&lMemoriza el patrón!'
    START_ANSWER_TITTLE: ''
    START_ANSWER_SUBTITLE: '&e&lIngresa el patrón!'
  CRYSTALS:
    ACTIONBAR: '&e<duration_millis>'
    FINISH: '&b&lᴄʀɪsᴛᴀʟᴇs &r&7➜&r&a Ronda finalizada! Tiempo: &e<duration_millis>&a!'
    NEW_RECORD: '&b&lᴄʀɪsᴛᴀʟᴇs &r&7➜&r&a Has alcanzado un nuevo record de tiempo:
      &e<duration_millis>&a!'
    NEW_RECORD_TITTLE: '&a&lNuevo record!'
    NEW_RECORD_SUBTITLE: '&e<duration_millis>'
    RESET_ITEM:
      NAME: '&eReiniciar'
      LORE:
        - ''
        - '&7Click derecho para reiniciar.'
        - ''
    LEAVE_ITEM:
      NAME: '&cSalir'
      LORE:
        - ''
        - '&7Click derecho para salir.'
        - ''
  DROPPER:
    # Items del inventario
    RESET_ITEM:
      NAME: '&e&lReiniciar Dropper'
      LORE:
        - '&7Click derecho para generar'
        - '&7un nuevo layout de obstáculos'
    LEAVE_ITEM:
      NAME: '&c&lSalir del Dropper'
      LORE:
        - '&7Click derecho para salir'
        - '&7del minijuego Dropper'

    # Mensajes de inicio (múltiples líneas)
    GAME_STARTED:
      - '&a¡Dropper iniciado! &7¡Evita los obstáculos y llega al agua!'
      - '&7Racha actual: &e<streak> &7| Mejor tiempo: &e<best_time>'
      - '&7Intento: &e<attempt> &7| ¡Buena suerte!'

    START_TITLE: '&6&lDROPPER'
    START_SUBTITLE: '&7¡Evita los obstáculos! Intento #<attempt>'

    # Victoria
    WIN:
      - '&a&l¡COMPLETADO!'
      - '&7Tiempo: &e<time>'
      - '&7Racha: &a<streak> victorias consecutivas'
      - '&7Intento: &e<attempt>'

    WIN_TITLE: '&a&l¡VICTORIA!'
    WIN_SUBTITLE: '&7Tiempo: &e<time> &7| Racha: &a<streak>'

    # Nuevo Record
    NEW_RECORD:
      - '&6&l¡NUEVO RECORD PERSONAL!'
      - '&7Nuevo mejor tiempo: &e<time>'
      - '&7Racha actual: &a<streak> victorias'
      - '&a¡Felicitaciones!'

    NEW_RECORD_TITLE: '&6&l¡NUEVO RECORD!'
    NEW_RECORD_SUBTITLE: '&7Tiempo: &e<time>'

    # Derrota
    LOSE:
      - '&c&l¡TOCASTE UN OBSTÁCULO!'
      - '&7Tiempo de supervivencia: &e<time>'
      - '&7Intento: &e<attempt>'

    LOSE_WITH_STREAK:
      - '&c&l¡TOCASTE UN OBSTÁCULO!'
      - '&7Tiempo de supervivencia: &e<time>'
      - '&4Racha perdida: &c<lost_streak> victorias'
      - '&7Intento: &e<attempt>'

    LOSE_TITLE: '&c&l¡DERROTA!'
    LOSE_SUBTITLE: '&7Supervivencia: &e<time>'

    # Mensajes motivacionales
    TRY_AGAIN:
      - '&7&o¡No te rindas!'
      - '&7&oUsa la flecha para reintentar con un nuevo layout'

    # Racha especial
    STREAK_MILESTONE:
      - '&6&l¡RACHA IMPRESIONANTE!'
      - '&e<streak> victorias consecutivas'
      - '&7¡Sigue así!'

    # Reset manual
    MANUAL_RESET:
      - '&e¡Dropper reiniciado!'
      - '&7Tiempo anterior: &e<time>'
      - '&7Nuevo intento: &e<attempt>'

    # Nuevo layout
    NEW_LAYOUT: '&7¡Nuevo layout de obstáculos generado!'

    # Salida
    QUIT:
      - '&cSaliendo del Dropper...'
      - '&7¡Gracias por jugar!'

    # Estadísticas adicionales (para comandos o GUIs)
    STATS_TOTAL_WINS: '&7Victorias totales: &a<wins>'
    STATS_TOTAL_ATTEMPTS: '&7Intentos totales: &e<attempts>'
    STATS_COMPLETION_RATE: '&7Tasa de éxito: &e<rate>%'
    STATS_CURRENT_STREAK: '&7Racha actual: &a<streak>'
    STATS_BEST_TIME: '&7Mejor tiempo: &e<time>'
  OSU:
    # Items del inventario
    RESET_ITEM:
      NAME: '&e&lReiniciar Juego'
      LORE:
        - '&7Click derecho para reiniciar'
        - '&7la partida actual'
    LEAVE_ITEM:
      NAME: '&c&lSalir del Juego'
      LORE:
        - '&7Click derecho para salir'
        - '&7del minijuego OSU'

    # Mensajes de inicio (múltiples líneas)
    GAME_STARTED:
      - '&a¡Juego OSU iniciado! &7Haz click izquierdo mirando a los bloques rojos.'
      - '&7Duración: &e<time> segundos'
      - '&7¡Cuidado! Los clicks fallidos cuentan como &cmiss&7.'

    START_TITLE: '&6&lOSU!'
    START_SUBTITLE: '&7¡Apunta y dispara!'

    # Objetivos
    NEW_TARGET: '&e¡Nuevo objetivo generado! &7(<current>/<max>)'
    TARGET_HIT: '&a✓ ¡OBJETIVO! &7+10 puntos &e(Puntuación: <score>)'
    MISS: '&c✗ MISS! &7(-1 punto) &e(Puntuación: <score>)'

    # Estadísticas actuales (múltiples líneas)
    CURRENT_STATS:
      - '&7&m---&r &6Estadísticas Actuales &7&m---'
      - '&7Aciertos: &a<hits> &7| Fallos: &c<misses>'
      - '&7Precisión: &e<accuracy>% &7| Puntuación: &e<score>'

    # Tiempo
    TIME_REMAINING: '&7Tiempo restante: &e<time> segundos'
    TIME_WARNING_30:
      - '&6¡30 segundos restantes!'
      - '&7¡Apunta con precisión!'
    TIME_WARNING_10:
      - '&c¡10 segundos!'
      - '&7¡Últimos disparos!'

    # Fin del juego (múltiples líneas)
    GAME_FINISHED:
      - '&a&l=== ¡JUEGO TERMINADO! ==='
      - '&7Puntuación final: &e<score>'
      - '&7Aciertos: &a<hits> &7| Fallos: &c<misses>'
      - '&7Total disparos: &e<total_shots>'
      - '&7Precisión: &e<accuracy>%'

    FINISH_TITLE: '&6¡Juego Terminado!'
    FINISH_SUBTITLE: '&7Puntuación: &e<score> &7| Precisión: &e<accuracy>'

    # Calificaciones
    GRADE_S_PLUS: '&6&lS+'
    GRADE_S: '&6&lS'
    GRADE_A: '&a&lA'
    GRADE_B: '&e&lB'
    GRADE_C: '&c&lC'
    GRADE_D: '&4&lD'
    GRADE_F: '&8&lF'

    # Records (múltiples líneas)
    NEW_SCORE_RECORD:
      - '&a&l¡NUEVO RECORD DE PUNTUACIÓN!'
      - '&e<score> puntos'
    NEW_ACCURACY_RECORD:
      - '&a&l¡NUEVO RECORD DE PRECISIÓN!'
      - '&e<accuracy>%'
    NEW_RECORD_TITLE: '&6&l¡NUEVO RECORD!'
    NEW_RECORD_SUBTITLE: '&e<score> puntos'

    # Otros
    GAME_RESET: '&e¡Juego reiniciado!'
    GAME_ENDED: '&cJuego OSU finalizado.'
    CURRENT_STATS_HEADER: '&7&m---&r &6Estadísticas Actuales &7&m---'
    CURRENT_STATS_HITS: '&7Aciertos: &a{0} &7| Fallos: &c{1}'
    CURRENT_STATS_ACCURACY: '&7Precisión: &e{0}% &7| Puntuación: &e{1}'
    FINAL_SCORE: '&7Puntuación final: &e{0}'
    FINAL_HITS: '&7Aciertos: &a{0} &7| Fallos: &c{1}'
    FINAL_SHOTS: '&7Total disparos: &e{0}'
    FINAL_ACCURACY: '&7Precisión: &e{0}%'
    FINAL_GRADE: '&7Calificación: {0}'
