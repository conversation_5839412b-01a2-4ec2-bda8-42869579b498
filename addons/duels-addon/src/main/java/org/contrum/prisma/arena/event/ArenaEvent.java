package org.contrum.prisma.arena.event;

import com.google.common.base.Preconditions;
import lombok.Getter;
import org.bukkit.event.Event;
import org.contrum.prisma.arena.DuelArena;

/**
 * Represents an event involving an {@link DuelArena}
 */
abstract class ArenaEvent extends Event {

    /**
     * The match involved in this event
     */
    @Getter private final DuelArena arena;

    // Constructor for the abstract class, takes an Arena object as a parameter
    ArenaEvent(DuelArena arena) {
        // Check that the provided Arena object is not null, using Guava's Preconditions
        this.arena = Preconditions.checkNotNull(arena, "arena");
    }

}
