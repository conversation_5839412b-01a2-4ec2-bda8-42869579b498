package org.contrum.prisma.match.phase;

import org.contrum.game.api.match.Match;
import org.contrum.game.core.match.phase.ConditionalMatchPhase;
import org.jetbrains.annotations.NotNull;

public class WaitingMatchPhase extends ConditionalMatchPhase<Match> {

    public WaitingMatchPhase(@NotNull Match match) {
        super(Match::isReady, match);
    }

    @Override
    public void startPhase() {}

    @Override
    public void tick() {
        super.tick();
    }

    @Override
    public void endPhase() {
        match.setActivePhase(new StartingPhase(match));
    }

    @Override
    public Match.MatchState getState() {
        return Match.MatchState.WAITING;
    }
}
