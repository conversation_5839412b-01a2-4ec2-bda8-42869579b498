package org.contrum.prisma.utils;

import com.fastasyncworldedit.core.util.TaskManager;
import com.sk89q.worldedit.EditSession;
import com.sk89q.worldedit.MaxChangedBlocksException;
import com.sk89q.worldedit.WorldEdit;
import com.sk89q.worldedit.bukkit.BukkitCommandSender;
import com.sk89q.worldedit.bukkit.BukkitWorld;
import com.sk89q.worldedit.bukkit.WorldEditPlugin;
import com.sk89q.worldedit.extent.clipboard.Clipboard;
import com.sk89q.worldedit.extent.clipboard.io.ClipboardFormat;
import com.sk89q.worldedit.extent.clipboard.io.ClipboardFormats;
import com.sk89q.worldedit.extent.clipboard.io.ClipboardReader;
import com.sk89q.worldedit.function.operation.ForwardExtentCopy;
import com.sk89q.worldedit.function.operation.Operation;
import com.sk89q.worldedit.function.operation.Operations;
import com.sk89q.worldedit.math.BlockVector3;
import com.sk89q.worldedit.regions.CuboidRegion;
import com.sk89q.worldedit.regions.Region;
import com.sk89q.worldedit.session.ClipboardHolder;
import com.sk89q.worldedit.world.block.BaseBlock;
import com.sk89q.worldedit.world.block.BlockTypes;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.contrum.game.api.match.Match;
import org.contrum.prisma.DuelsAddon;
import org.contrum.prisma.arena.ArenaSchematic;
import org.contrum.prisma.arena.ArenaService;
import org.contrum.prisma.arena.DuelArena;

import java.awt.geom.AffineTransform;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Map;

public final class WorldEditUtils {

    private static EditSession session;
    private static com.sk89q.worldedit.world.World worldEditWorld;

    private WorldEditUtils() {}

    public static void primeWorldEditApi() {
        if (session != null) return;

        session = WorldEdit.getInstance().newEditSessionBuilder()
                .world(worldEditWorld = new BukkitWorld(ArenaService.getArenaWorld()))
                .maxBlocks(Integer.MAX_VALUE)
                .fastMode(true)
                .actor(new BukkitCommandSender(WorldEditPlugin.getInstance(), Bukkit.getConsoleSender()))
                .build();
    }

    public static Clipboard paste(DuelsAddon plugin, ArenaSchematic arena, BlockVector3 pasteAt) throws IOException {
        primeWorldEditApi();

        // systems like the ArenaGrid assume that pastes will 'begin' directly at the Vector
        // provided. to ensure we can do this, we manually clear any offset (distance from
        // corner of schematic to player) to ensure our pastes aren't dependant on the
        // location of the player when copied

        Clipboard clipboard;

        File file = arena.getSchematicFile();

        ClipboardFormat format = ClipboardFormats.findByFile(file);
        try (ClipboardReader reader = format.getReader(new FileInputStream(file))) {
            clipboard = reader.read();

            AffineTransform transform = new AffineTransform();

            ForwardExtentCopy copy = new ForwardExtentCopy(clipboard, clipboard.getRegion(), clipboard.getOrigin(), session, pasteAt);
            if (!transform.isIdentity())
                transform.setTransform(transform);

            Operations.complete(copy);
        } finally {
            session.flushQueue();
        }

        return clipboard;
    }


    public static Clipboard paste(File schematicFile, BlockVector3 pasteAt) throws IOException {
        primeWorldEditApi();

        Clipboard clipboard;

        ClipboardFormat format = ClipboardFormats.findByFile(schematicFile);

        try (ClipboardReader reader = format.getReader(new FileInputStream(schematicFile))) {
            clipboard = reader.read();

            BukkitWorld world = new BukkitWorld(ArenaService.getArenaWorld());

            try (EditSession editSession = WorldEdit.getInstance().newEditSession(world)) {
                Operation operation = new ClipboardHolder(clipboard)
                        .createPaste(editSession)
                        .to(pasteAt)
                        // configure here
                        .build();
                Operations.complete(operation);
            }
        }

        return clipboard;
    }

    public static void setBlock(Location location, Material material) {
        primeWorldEditApi();

        //location.getBlock().setType(material);

        session.setBlock(location.getBlockX(), location.getBlockY(), location.getBlockY(),
                BlockTypes.parse(material.name()).getDefaultState());



        session.flushQueue();
    }

    public static void setBlocks(Map<Location, Material> blocksChanges) {
      TaskManager.taskManager().async(() -> {
        primeWorldEditApi();

      //location.getBlock().setType(material);

      for (Map.Entry<Location, Material> entry : blocksChanges.entrySet()) {
          Location location = entry.getKey();
          Material material = entry.getValue();

          session.setBlock(location.getBlockX(), location.getBlockY(), location.getBlockY(),
                  BlockTypes.parse(material.name()).getDefaultState());
      }


      session.flushQueue();
      });
  }

    public static void save(ArenaSchematic schematic, BlockVector3 saveFrom) throws Exception {
        primeWorldEditApi();
    }

    public static void clear(BlockVector3 lower, BlockVector3 upper) {
        primeWorldEditApi();

        session.setBlocks((Region) new CuboidRegion(worldEditWorld, lower, upper), BlockTypes.AIR);
        session.flushQueue();
    }

    public static void clear(DuelArena arena) {
        primeWorldEditApi();

        TaskManager.taskManager().async(() ->
                arena.forEachBlock(block ->
                        session.setBlock(BlockVector3.at(block.getX(), block.getY(), block.getZ()), BlockTypes.AIR)));

        session.flushQueue();
    }

    public static void clear(Match<?> match) {
        primeWorldEditApi();

        TaskManager.taskManager().async(() -> {
            for (Location location : match.getBlocksPlaced()) {
                session.setBlock(BlockVector3.at(location.getX(), location.getY(), location.getZ()), BlockTypes.AIR);
            }

            for (Map.Entry<Location, Material> entry : match.getChangedBlocks().entrySet()) {
                session.setBlock(BlockVector3.at(entry.getKey().getX(), entry.getKey().getY(), entry.getKey().getZ()),
                        BlockTypes.get(entry.getValue().name()));
            }
        });

        session.flushQueue();
    }

    public static BlockVector3 readSchematicSize(ArenaSchematic arena) {
        return BlockVector3.at(0, 0 ,0);
    }

    public static Location vectorToLocation(DuelsAddon plugin, BlockVector3 vector) {
        return new Location(ArenaService.getArenaWorld(),
                vector.getX(),
                vector.getY(),
                vector.getZ());
    }
}