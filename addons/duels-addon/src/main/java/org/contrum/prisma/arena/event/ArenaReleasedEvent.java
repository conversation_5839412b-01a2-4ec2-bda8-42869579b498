package org.contrum.prisma.arena.event;

import lombok.Getter;
import org.bukkit.event.HandlerList;
import org.contrum.prisma.arena.DuelArena;

/**
 * Called when an {@link DuelArena} is done
 */
public final class ArenaReleasedEvent extends ArenaEvent {

    @Getter private static final HandlerList handlerList = new HandlerList();

    // Constructor for the event, takes an Arena object as a parameter
    public ArenaReleasedEvent(DuelArena arena) {
        // Call the constructor of the superclass (ArenaEvent) with the provided Arena
        super(arena);
    }

    // Override method from the superclass to get the list of registered handlers for this event
    @Override
    public HandlerList getHandlers() {
        // Return the static handlerList that holds all the registered handlers for this event
        return handlerList;
    }

}
