package org.contrum.prisma.utils;

import lombok.experimental.UtilityClass;
import org.bukkit.Bukkit;
import org.bukkit.plugin.Plugin;
import org.contrum.chorpu.TaskUtil;

@UtilityClass
public class SafeTask {
    public void run(Plugin plugin, Runnable runnable) {
        try {
            TaskUtil.run(plugin, runnable);
        } catch (Exception | Error e ) {
            System.out.println("A task could not be registered cause: " + e.getMessage());
            runnable.run();
        }
    }

    public void ensureSync(Plugin plugin, Runnable runnable) {
        if (Bukkit.isPrimaryThread()) {
            runnable.run();
        } else {
            run(plugin, runnable);
        }
    }
}
