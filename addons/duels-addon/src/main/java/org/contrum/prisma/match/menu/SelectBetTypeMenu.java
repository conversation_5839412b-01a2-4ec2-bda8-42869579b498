package org.contrum.prisma.match.menu;

import lombok.RequiredArgsConstructor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.Menu;
import org.contrum.prisma.DuelsAddon;
import org.contrum.prisma.match.BetType;
import org.contrum.prisma.utils.menus.buttons.BackButton;
import org.contrum.tritosa.Translator;

import java.util.Map;
import java.util.function.Consumer;

@RequiredArgsConstructor
public class SelectBetTypeMenu extends Menu {

    private final Translator translator;
    private final Consumer<BetType> onSelect;
    private final Menu backMenu;

    @Override
    public int getRows(Player player) {
        return 3;
    }

    @Override
    public Map<Integer, Button> getButtons(Player player) {
        return Map.of(
                0, new BackButton(translator, backMenu),

                getSlot(1, 2), Button.of(new ItemBuilder(Material.CHEST)
                        .setName(
                                translator.getAsText(player, "DUELS.MENU.SELECT_BET_TYPE.ITEMS.NAME"))
                        .addLore(
                                translator.getListString(player, "DUELS.MENU.SELECT_BET_TYPE.ITEMS.LORE")
                        ).build(), other -> {
                    onSelect.accept(BetType.ITEMS);
                }),
                getSlot(1, 4), Button.of(new ItemBuilder(Material.GOLD_INGOT)
                        .setName(translator.getAsText(player, "DUELS.MENU.SELECT_BET_TYPE.COINS.NAME"))
                        .addLore(
                               translator.getListString(player, "DUELS.MENU.SELECT_BET_TYPE.COINS.LORE")
                        ).build(), other -> {
                    onSelect.accept(BetType.COINS);
                }),
                getSlot(1, 6), Button.of(new ItemBuilder(Material.BARRIER)
                        .setName(translator.getAsText(player, "DUELS.MENU.SELECT_BET_TYPE.NONE.NAME"))
                        .addLore(
                                translator.getListString(player, "DUELS.MENU.SELECT_BET_TYPE.NONE.LORE")
                        ).build(), other -> {
                    onSelect.accept(BetType.NONE);
                })

        );
    }

    @Override
    public String getTitle(Player player) {
        return "";
    }
}
