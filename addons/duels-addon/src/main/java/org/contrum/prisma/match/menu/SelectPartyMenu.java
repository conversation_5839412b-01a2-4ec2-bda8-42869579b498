package org.contrum.prisma.match.menu;

import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.contrum.chorpu.inventory.ItemBuilder;
import org.contrum.chorpu.menu.button.Button;
import org.contrum.chorpu.menu.impl.PaginatedMenu;
import org.contrum.chorpu.menu.storage.StorageMenu;
import org.contrum.prisma.DuelsAddon;
import org.contrum.prisma.party.Party;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@RequiredArgsConstructor
public class SelectPartyMenu extends PaginatedMenu {

    private final DuelsAddon plugin;
    private final Predicate<Party> filter;
    private final Consumer<Party> consumer;

    @Override
    public List<Button> getPaginatedButtons(Player player) {
        return plugin.getPartyService().getParties().stream()
                .filter(filter)
                .map(PartyButton::new)
                .collect(Collectors.toList());
    }

    @Override
    public Map<Integer, Button> getGlobalButtons(Player player) {
        Map<Integer, Button> buttons = Maps.newHashMap();

        return buttons;
    }

    @Override
    public int getRows(Player player) {
        return 6;
    }

    @Override
    public String getTitle(Player player) {
        return "Online parties";
    }

    @Override
    public StorageMenu.FillType getFillType() {
        return StorageMenu.FillType.ONLY_CORNERS;
    }

    @RequiredArgsConstructor
    public class PartyButton extends Button {

        private final Party party;

        @Override
        public ItemStack getDisplayItem(Player player) {
            LocalPlaceholders placeholders = LocalPlaceholders.builder()
                    .add("<party_name>", party.getPlayerLeader().getName())
                    .add("<party_members>", party.getFormatedMembers())
                    .add("<party_leader>", party.getPlayerLeader().getName());

            return new ItemBuilder(Material.PLAYER_HEAD)
                    .setName(party.getPlayerLeader().getName())
                    .addLore(
                            plugin.getServices().getTranslator().getListString(player, "PARTY.MENU.LORE", placeholders)
                    ).build();
        }

        @Override
        public void clicked(Player player) {
            consumer.accept(party);
        }
    }
}
