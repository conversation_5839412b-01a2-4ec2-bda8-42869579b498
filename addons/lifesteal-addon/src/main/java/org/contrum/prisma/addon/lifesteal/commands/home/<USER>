package org.contrum.prisma.addon.lifesteal.commands.home;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.contrum.prisma.addon.lifesteal.Addon;
import org.contrum.prisma.addon.lifesteal.commands.home.menu.HomesMenu;
import org.contrum.prisma.addon.lifesteal.profile.ProfileLifeStealMetadata;
import org.contrum.prisma.addon.lifesteal.systems.HomeSystem;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.tritosa.Translator;
import org.contrum.tritosa.placeholder.LocalPlaceholders;

@CommandAlias("home")
public class HomesCommand extends BaseCommand {

    @Dependency private Addon addon;
    @Dependency private Translator translator;

    @Default @CommandAlias("homes")
    public void menu(Player player) {
        new HomesMenu(addon).open(player);
    }

    @Default @CommandCompletion("@homes")
    public void menu(Player player, String homeName) {
        ProfileLifeStealMetadata metadata = addon.getLifestealMetadata(player);

        LocalPlaceholders placeholders = LocalPlaceholders.builder().add("<home_name>", homeName);
        Location home = metadata.getHome(homeName);
        if (home == null) {
            translator.send(player, "COMMANDS.HOMES.DOESNT_EXIST", placeholders);
            WorldUtils.playSound(Sound.ENTITY_VILLAGER_NO, player);
            return;
        }

        translator.send(player, "COMMANDS.HOMES.TELEPORT", placeholders);
        addon.getWorldUtils().teleportWithCooldown(player, home);
    }

    @Subcommand("set") @CommandAlias("sethome")
    public void create(Player player, String homeName) {
        ProfileLifeStealMetadata metadata = addon.getLifestealMetadata(player);

        addon.getSystem(HomeSystem.class).addHome(player, homeName, player.getLocation());
    }

    @Subcommand("delete") @CommandCompletion("@homes") @CommandAlias("delhome")
    public void delete(Player player, String homeName) {
        ProfileLifeStealMetadata metadata = addon.getLifestealMetadata(player);

        addon.getSystem(HomeSystem.class).removeHome(player, homeName);
    }
}
