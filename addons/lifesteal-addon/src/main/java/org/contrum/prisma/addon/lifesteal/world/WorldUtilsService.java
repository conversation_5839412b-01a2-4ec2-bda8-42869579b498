package org.contrum.prisma.addon.lifesteal.world;

import lombok.Getter;
import org.bukkit.Location;
import org.bukkit.Sound;
import org.bukkit.entity.Player;
import org.contrum.prisma.actionbar.bars.impl.placeholder.PlaceHolderActionBar;
import org.contrum.prisma.addon.lifesteal.Addon;
import org.contrum.prisma.addon.lifesteal.settings.LifeStealSettings;
import org.contrum.prisma.profile.metadata.server.ProfilePaperMetadata;
import org.contrum.prisma.utils.WorldUtils;
import org.contrum.prisma.utils.tick.TickedCooldown;
import org.contrum.prisma.utils.time.TimeUtils;

import java.time.Duration;

@Getter
public class WorldUtilsService {

    private final Addon addon;

    public WorldUtilsService(Addon addon) {
        this.addon = addon;
    }

    public void teleportWithCooldown(Player player, Location location) {
        this.teleportWithCooldown(player, location, TimeUtils.parseDuration(LifeStealSettings.TELEPORT_DEFAULT_WAIT_TIME), null);
    }

    public void teleportWithCooldown(Player player, Location location, Duration cooldown) {
        this.teleportWithCooldown(player, location, cooldown, null);
    }

    public void teleportWithCooldown(Player player, Location location, Duration cooldown, Runnable onFinish) {
        ProfilePaperMetadata meta = addon.getPaperMetadata(player);

        if (meta.hasCooldown("Teleport")) {
            addon.getTranslator().send(player, "TELEPORT.COOLDOWN", meta.getCooldown("Teleport").getTimeLeftAsDuration());
            WorldUtils.playSound(Sound.ENTITY_VILLAGER_NO, player);
            return;
        }
        meta.setCooldown("Teleport", Duration.ofSeconds(10));

        PlaceHolderActionBar actionbar = new PlaceHolderActionBar(addon.getTranslator(), "TELEPORT", "TELEPORT.ACTIONBAR", cooldown.plusSeconds(1), 8);
        meta.addActionBar(player, actionbar);
        actionbar.start();


        if (meta.isTagged()) {
            addon.getTranslator().send(player, "ERRORS.COMBAT_TAGGED");
            return;
        }

        Location playerLocation = player.getLocation();
        new TickedCooldown(addon, cooldown)
                .onTick((c, tick) -> {
                    if (!player.isOnline()) {
                        c.cancel();
                        return;
                    }

                    //Check for location change or combat-tag
                    if (hasExplicitlyChangedPosition(playerLocation, player.getLocation()) || meta.isTagged()) {
                        addon.getTranslator().send(player, "TELEPORT.CANCELLED");
                        WorldUtils.playSound(Sound.ENTITY_VILLAGER_NO, player);
                        c.cancel();
                        actionbar.cancel();
                        return;
                    }

                    if (tick % 21 == 0 || tick == 1) {
                        addon.getTranslator().send(player, "TELEPORT.MESSAGE", c.getTimeLeftAsDuration().plusSeconds(1));
                        WorldUtils.playSound(Sound.BLOCK_NOTE_BLOCK_PLING, player);
                    }
                })

                .onFinish(() -> {
                    if (player.isOnline()) {
                        player.teleportAsync(location);
                        WorldUtils.playSound(Sound.ENTITY_PLAYER_LEVELUP, player, location, 1, 0);

                        if (onFinish != null)
                            onFinish.run();
                    }
                });
    }

    public boolean hasExplicitlyChangedPosition(Location from, Location to) {
        return from.getBlockX() != to.getBlockX() || from.getBlockY() != to.getBlockY() || from.getBlockZ() != to.getBlockZ();
    }
}
