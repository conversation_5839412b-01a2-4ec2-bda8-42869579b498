package org.contrum.prisma.velocity.commands;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.CommandAlias;
import co.aikar.commands.annotation.Default;
import co.aikar.commands.annotation.Dependency;
import com.velocitypowered.api.proxy.Player;
import com.velocitypowered.api.proxy.ProxyServer;
import com.velocitypowered.api.proxy.server.RegisteredServer;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.prisma.velocity.server.VelocityServersService;
import org.contrum.prisma.velocity.util.CC;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@CommandAlias("hub|lobby")
public class HubCommand extends BaseCommand {

    @Dependency private VelocityServersService serversService;
    @Dependency private ProxyServer proxy;

    @Default
    public void hub(Player player) {
        List<PrismaServer> servers = new ArrayList<>(serversService.getServers(PrismaServer.ServerType.LOBBY));
        if (servers.isEmpty()) {
            player.sendMessage(CC.translate("&cNo hay lobbys disponibles por el momento! Por favor, aguarda unos momentos y vuelve a intentarlo!"));
            return;
        }

        Optional<PrismaServer> server = servers.stream()
                .min(Comparator.comparingInt(s -> s.getOnlinePlayers().size()));

        Optional<RegisteredServer> serverInfo = Optional.empty();
        while (serverInfo.isEmpty()) {
            if (servers.isEmpty()) {
                player.sendMessage(CC.translate("&cNo hay lobbys disponibles por el momento! Por favor, aguarda unos momentos y vuelve a intentarlo!"));
                return;
            }
            Optional<PrismaServer> s = servers.stream()
                    .min(Comparator.comparingInt(sv -> sv.getOnlinePlayers().size()));

            serverInfo =  proxy.getServer(s.get().getName());
            servers.remove(s.get());
        }

        player.createConnectionRequest(serverInfo.get()).connect();
        player.sendMessage(CC.translate("&aConectando al lobby: " + serverInfo.get().getServerInfo().getName()));
    }
}
