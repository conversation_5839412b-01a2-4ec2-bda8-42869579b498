package org.contrum.prisma.velocity.queue.packet;

import lombok.RequiredArgsConstructor;
import org.contrum.prisma.queue.Queue;
import org.contrum.prisma.queue.redis.packet.QueueTickPacket;
import org.contrum.prisma.utils.redis.pyrite.packet.PacketContainer;
import org.contrum.prisma.utils.redis.pyrite.packet.RedisPacketListener;
import org.contrum.prisma.velocity.queue.VelocityQueueService;

import java.util.List;

@RequiredArgsConstructor
public class VelocityQueuePacketListener implements PacketContainer {

    private final VelocityQueueService queueService;

    @RedisPacketListener
    public void onTick(QueueTickPacket packet) {
        Queue queue = queueService.getQueue(packet.getQueueUUID());
        List<Queue.QueueUser> users = packet.getSerializedPlayers().stream().map(Queue.QueueUser::new).toList();

        queueService.trySendNext(queue, users);
    }
}
