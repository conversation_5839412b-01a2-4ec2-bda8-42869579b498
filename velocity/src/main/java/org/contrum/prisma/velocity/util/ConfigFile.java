package org.contrum.prisma.velocity.util;

import com.velocitypowered.api.plugin.PluginContainer;
import lombok.Getter;
import org.bspfsystems.yamlconfiguration.configuration.InvalidConfigurationException;
import org.bspfsystems.yamlconfiguration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;

@Getter
public class ConfigFile {
    private final PluginContainer plugin;
    private String fileName;
    private File configFile;
    private YamlConfiguration config;

    public ConfigFile(PluginContainer plugin, String fileName) {
        this.plugin = plugin;
        this.fileName = fileName;
        this.configFile = new File("plugins/" + plugin.getDescription().getId() + "/" + this.fileName);
        this.config = new YamlConfiguration();

        this.saveDefaultConfig();

        try {
            this.config.load(configFile);
        } catch (IOException | InvalidConfigurationException e) {
            e.printStackTrace();
        }
    }

    public void reload() {
        if (this.configFile == null) {
            this.configFile = new File("plugins/" + plugin.getDescription().getId() + "/" + this.fileName);
        }

        try (InputStream inputStream = Files.newInputStream(this.configFile.toPath())) {
            this.config = new YamlConfiguration();
            this.config.load(configFile);
        } catch (IOException | InvalidConfigurationException e) {
            System.out.println("Error loading configuration file: " + e.getMessage());
        }
    }

    public void saveDefaultConfig() {
        if (this.configFile == null) {
            this.configFile = new File("plugins/" + plugin.getDescription().getId() + "/" + this.fileName);
        }

        if (!this.configFile.exists()) {
            try (InputStream input = getClass().getResourceAsStream("/" + fileName)) {
                if (input != null) {
                    Files.copy(input, this.configFile.toPath());
                } else {
                    System.out.println("Default configuration file not found in resources: " + fileName);
                }
            } catch (IOException e) {
                System.out.println("Error saving default configuration file: " + e.getMessage());
            }
        }
    }
}
