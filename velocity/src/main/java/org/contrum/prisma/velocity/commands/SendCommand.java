package org.contrum.prisma.velocity.commands;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import com.velocitypowered.api.command.CommandSource;
import com.velocitypowered.api.proxy.Player;
import com.velocitypowered.api.proxy.ProxyServer;
import com.velocitypowered.api.proxy.server.RegisteredServer;
import org.contrum.prisma.redis.RedisBackend;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.prisma.utils.user.SimpleUser;
import org.contrum.prisma.server.packet.ServerConnectRequestPacket;
import org.contrum.prisma.velocity.server.VelocityServersService;
import org.contrum.prisma.velocity.util.CC;

import java.util.Optional;

@CommandAlias("send|psend") @CommandPermission("core.bungee.command.send")
public class SendCommand extends BaseCommand {

    @Dependency private ProxyServer proxy;
    @Dependency private VelocityServersService serversService;
    @Dependency private RedisBackend redisBackend;

    @Default @CommandCompletion("@proxyplayers @servers") @Syntax("<name> <server>")
    public void find(CommandSource sender, String name, String server) {
        PrismaServer proxyServer = serversService.getPlayerProxy(name);

        if (proxyServer == null) {
            sender.sendMessage(CC.translate("&cUser is not online!"));
            return;
        }

        Optional<RegisteredServer> serverInfo = proxy.getServer(server);

        if (serverInfo.isEmpty()) {
            sender.sendMessage(CC.translate("&cServer not found!"));
            return;
        }

        //Check if player is on this proxy
        if (serversService.getCurrentServer().equals(proxyServer)) {
            Optional<Player> player = proxy.getPlayer(name);
            if (player.isPresent()) {

                player.get().createConnectionRequest(serverInfo.get()).connect();
                sender.sendMessage(CC.translate("&eTrying to send " + player.get().getUsername() + " to " + serverInfo.get().getServerInfo().getName() + "..."));
                return;
            }
        }

        //Check if player is online
        SimpleUser user = serversService.getUser(name);
        if (user == null) {
            sender.sendMessage(CC.translate("&cUser is not online!"));
            return;
        }

        //Send packet
        redisBackend.sendPacket(new ServerConnectRequestPacket(user, server));
    }
}
