package org.contrum.prisma.velocity.commands;

import co.aikar.commands.BaseCommand;
import co.aikar.commands.annotation.*;
import com.velocitypowered.api.command.CommandSource;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.prisma.velocity.VelocityServices;
import org.contrum.prisma.velocity.server.VelocityServersService;
import org.contrum.prisma.velocity.util.CC;

@CommandAlias("find|f|pfind") @CommandPermission("core.bungee.command.find")
public class FindCommand extends BaseCommand {

    @Dependency private VelocityServersService serversService;

    @Default @CommandCompletion("@proxyplayers")
    public void find(CommandSource sender, String name) {
        PrismaServer proxy = serversService.getPlayerProxy(name);
        PrismaServer server = serversService.getPlayerServer(name);

        if (proxy == null) {
            sender.sendMessage(CC.translate("&cUser is not online!"));
            return;
        }

        sender.sendMessage(CC.translate("&e" + name + " is online at: " + proxy.getName() + ", " + (server == null ? "unknown" : server.getName())));
    }
}