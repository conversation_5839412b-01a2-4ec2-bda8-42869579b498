package org.contrum.prisma.velocity.profile.listener;

import com.google.common.collect.Lists;
import com.velocitypowered.api.event.ResultedEvent;
import com.velocitypowered.api.event.Subscribe;
import com.velocitypowered.api.event.connection.DisconnectEvent;
import com.velocitypowered.api.event.connection.LoginEvent;
import com.velocitypowered.api.event.player.KickedFromServerEvent;
import com.velocitypowered.api.proxy.Player;
import com.velocitypowered.api.proxy.ServerConnection;
import net.kyori.adventure.text.Component;
import net.kyori.adventure.text.TextComponent;
import net.kyori.adventure.text.format.NamedTextColor;
import net.kyori.adventure.text.format.TextColor;
import net.luckperms.api.LuckPermsProvider;
import org.contrum.chorpu.TaskUtil;
import org.contrum.prisma.profile.Profile;
import org.contrum.prisma.profile.ProfileService;
import org.contrum.prisma.punishment.Punishment;
import org.contrum.prisma.punishment.PunishmentType;
import org.contrum.prisma.velocity.VelocityServices;
import org.contrum.prisma.velocity.util.CC;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

public class ProfileListener {

    private final VelocityServices services;
    private final ProfileService profileService;

    public ProfileListener(VelocityServices services) {
        this.services = services;
        this.profileService = services.getProfileService();
    }

    @Subscribe
    public void onPostLogin(LoginEvent event) {
        Player player = event.getPlayer();
        CompletableFuture<Profile> future = profileService.join(player.getUniqueId());
        if (future == null) {
            event.setResult(ResultedEvent.ComponentResult.denied(CC.translate("&cHas intentado cargar tu perfil demasiadas veces! Espera 1 minuto antes de volver a intentarlo!")));
            return;
        }

        future.whenCompleteAsync((profile, throwable) -> {

            if (throwable != null) {
                throwable.printStackTrace();
                return;
            }

            if (profile == null) {
                return;
            }

            profile.setName(player.getUsername());
            profile.setGlobalLastSeen(System.currentTimeMillis());

            profileService.getOnlineProfiles().put(player.getUniqueId(), profile);

            List<String> permissions = Lists.newArrayList();
            permissions.addAll(profile.getActiveGrant().getRank().getAllBungeePermissions());

            LuckPermsProvider.get().getUserManager().modifyUser(player.getUniqueId(), user -> {
                permissions.forEach(p -> user.data().add(net.luckperms.api.node.Node.builder(p).build()));
            });

            //Update proxy player list for all servers
            CompletableFuture.runAsync(() -> services.getServersService().sendServerInfo(), services.getPrismaAsyncExecutor());
        });
    }

    @Subscribe
    public void serverSwitch(KickedFromServerEvent event) {
        Optional<ServerConnection> optCurrentServer = event.getPlayer().getCurrentServer();
        if (optCurrentServer.isEmpty()) {
            return;
        }

        ServerConnection currentServer = optCurrentServer.get();
        if (currentServer.getServerInfo().getName().toLowerCase().contains("limbo")) {
            event.getPlayer().disconnect(event.getServerKickReason().orElse(Component.text("Unable to connect to the lobby server").color(NamedTextColor.RED)));
        }
    }

    @Subscribe
    public void onDisconnect(DisconnectEvent event) {
        Player player = event.getPlayer();
        Profile profile = profileService.getProfile(player.getUniqueId());
        if (profile == null)
            return;

        profileService.leave(profile.getUniqueId(), false);
    }
}
