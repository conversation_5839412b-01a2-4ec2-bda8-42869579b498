package org.contrum.prisma.sync.packet;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.contrum.prisma.utils.redis.pyrite.Packet;

import java.util.UUID;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public class PlayerChangePasswordPacket extends Packet {

    private UUID uuid;
    public String currentPassword;
    public String newPassword;

}
