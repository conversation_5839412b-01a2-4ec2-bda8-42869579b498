package org.contrum.prisma.timer.packets;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.bson.Document;
import org.contrum.prisma.utils.redis.pyrite.Packet;

@NoArgsConstructor
@AllArgsConstructor
@Getter
public class TimerUpdatePacket extends Packet {
    private Document timer;
    private Action action;

    public enum Action {
        CREATE,
        UPDATE,
        REMOVE;
    }
}
