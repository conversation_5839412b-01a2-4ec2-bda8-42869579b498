package org.contrum.prisma.timer.impl;

import lombok.Getter;
import lombok.Setter;
import org.bson.Document;
import org.contrum.prisma.utils.serialize.DocumentSerialized;

import java.time.Duration;
import java.util.UUID;

@Getter @Setter
public class Timer implements DocumentSerialized {
    private final UUID uuid;
    private final String name;

    private Duration duration;
    private Duration announcementInterval;
    private String text;
    private String scoreboardText;

    private int announceSpamAmount = 1;

    public Timer(Document document) {
        this.name = document.getString("name");
        this.uuid = UUID.fromString(document.getString("UUID"));
        this.duration = Duration.ofMillis(document.get("duration", Number.class).longValue());
        this.announcementInterval = Duration.ofMillis(document.get("announcementInterval", Number.class).longValue());

        this.text = document.getString("text");
        this.scoreboardText = document.getString("scoreboardText");

        if (document.containsKey("announceSpamAmount")) {
            this.announceSpamAmount = document.get("announceSpamAmount", Number.class).intValue();
        }
    }

    public Timer(String name, Duration duration) {
        this.name = name;
        this.uuid = UUID.randomUUID();
        this.duration = duration;
        this.announcementInterval = Duration.ofMinutes(15);
    }

    @Override
    public Document serialize() {
        return new Document()
                .append("name", name)
                .append("UUID", uuid.toString())
                .append("duration", duration.toMillis())
                .append("announcementInterval", announcementInterval.toMillis())
                .append("text", text != null ? text : "")
                .append("scoreboardText", scoreboardText != null ? scoreboardText : "")
                .append("announceSpamAmount", announceSpamAmount);
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || this.getUuid() == null || getClass() != obj.getClass()) return false;

        Timer that = (Timer) obj;
        return this.getUuid().equals(that.getUuid());
    }

    @Override
    public int hashCode() {
        return uuid.hashCode();
    }
}
