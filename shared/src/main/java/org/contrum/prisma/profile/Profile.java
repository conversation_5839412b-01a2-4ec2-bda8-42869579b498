package org.contrum.prisma.profile;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.mongodb.client.model.Filters;
import lombok.Getter;
import lombok.Setter;
import org.bson.Document;
import org.contrum.prisma.grant.Grant;
import org.contrum.prisma.grant.packet.PlayerGrantPacket;
import org.contrum.prisma.mongo.MongoBackend;
import org.contrum.prisma.permissions.PermissionHandler;
import org.contrum.prisma.profile.chat.ChatType;
import org.contrum.prisma.profile.logs.reports.ReportLog;
import org.contrum.prisma.profile.metadata.ProfileMetadata;
import org.contrum.prisma.profile.metadata.ProfileServerMetadata;
import org.contrum.prisma.profile.notes.Note;
import org.contrum.prisma.profile.statistics.ProfileStatistics;
import org.contrum.prisma.punishment.Punishment;
import org.contrum.prisma.punishment.PunishmentType;
import org.contrum.prisma.rank.Rank;
import org.contrum.prisma.rank.RankService;
import org.contrum.prisma.server.PrismaServer;
import org.contrum.prisma.server.ServersService;
import org.contrum.prisma.service.ServiceManager;
import org.contrum.prisma.utils.Cooldown;
import org.contrum.prisma.utils.ServerContext;
import org.contrum.prisma.utils.serialize.DocumentSerialized;
import org.contrum.prisma.utils.user.SimpleUser;
import org.contrum.tritosa.holder.LanguageHolder;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;

import java.lang.reflect.InvocationTargetException;
import java.time.Duration;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Predicate;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.stream.Collectors;

@Getter
@Setter
public class Profile implements LanguageHolder {

    private static final Logger logger = Logger.getLogger("prisma-core");

    private boolean metadataLoaded = false;
    private boolean dataLoaded = false;

    private UUID uniqueId;
    private String name;

    private String serverName;

    private long globalFirstSeen;
    private long globalLastSeen;

    private ProfileStatistics statistics = new ProfileStatistics();

    private final Map<String, Cooldown> cooldows = Maps.newHashMap();

    private final Map<String, Document> globalSerializedMetadata = Maps.newHashMap();
    private final Map<String, Map<String, Document>> serverSerializedMetadata = Maps.newHashMap();

    private final Map<Class<?>, ProfileMetadata> globalMetadata = Maps.newHashMap();
    private final Map<String, Map<Class<?>, ProfileServerMetadata>> serverMetadata = Maps.newHashMap();

    private final Set<String> addresses = Sets.newHashSet();

    private String lastAddress;
    private PrismaServer lastServer;

    private final List<Grant> grants = Lists.newArrayList();
    private Grant activeGrant;

    private final Set<Punishment> punishments = Sets.newHashSet();

    private final Set<String> personalPermissions = Sets.newHashSet();
    private PermissionHandler permissionHandler;

    private final List<SimpleUser> ignoredUsers = Lists.newArrayList();

    private Document toMigrate;
    private Profile chatter;

    private ChatType chatType = ChatType.PUBLIC;
    private boolean tradeIgnore;

    private List<Note> notes = new ArrayList<>();

    private final Map<ServerContext, String> activesTags = Maps.newTreeMap(new ServerContext.ServerContextComparator());
    private List<String> ownedTags = new ArrayList<>();

    private final Set<UUID> trackedQueues = new HashSet<>();

    // Logs
    private List<ReportLog> reportLogs = new ArrayList<>();

    //Temporal data
    private boolean disguised;
    private String disguiseNick;
    private Rank disguiseRank;

    private String nickColor = "";

    private String discordId;

    public Profile(String name) {
        this.name = name;
    }

    public Profile(UUID uniqueId) {
        this.uniqueId = uniqueId;
    }

    public void loadData(Document document, ServiceManager serviceManager) {
        if (document == null) {
            return;
        }

        RankService rankService = serviceManager.getRankService();
        ServersService serversService = serviceManager.getServersService();
        PrismaServer currentServer = serversService.getCurrentServer();

        this.serverName = currentServer.getName();

        if (this.uniqueId == null)
            this.uniqueId = UUID.fromString(document.getString("uniqueId"));
        this.name = document.getString("name");

        List<Document> grants = document.getList("grants", Document.class);

        this.grants.clear();
        Set<UUID> seenIds = new HashSet<>();
        grants.forEach(grant -> {
            if (grant == null) return;
            Grant newGrant = new Grant(grant, rankService, serversService);
            if (seenIds.add(newGrant.getId()))
                this.grants.add(newGrant);
        });

        updateActiveGrant(serviceManager);

        this.punishments.clear();
        if (document.containsKey("punishments")) {
            List<Document> punishments = document.getList("punishments", Document.class);

            punishments.forEach(doc -> {
                this.punishments.add(new Punishment(doc));
            });
        }

        if (document.containsKey("lastSeen")) {
            this.globalLastSeen = document.get("lastSeen", Number.class).longValue();
        }

        if (document.containsKey("globalFirstSeen")) {
            this.globalFirstSeen = document.get("globalFirstSeen", Number.class).longValue();
        } else {
            this.globalFirstSeen = System.currentTimeMillis();
        }

        if (document.containsKey("lastServer")) {
            this.lastServer = serversService.getServer(document.getString("lastServer"));
        }

        if (document.containsKey("lastAddress")) {
            this.lastAddress = document.getString("lastAddress");
        }

        this.addresses.clear();
        if (document.containsKey("addresses")) {
            try {
                this.addresses.addAll(document.getList("addresses", String.class));
            } catch (Exception ignore) {}
        }

        this.personalPermissions.clear();
        if (document.containsKey("personalPermissions")) {
            this.personalPermissions.addAll(document.getList("personalPermissions", String.class));
        }

        this.ignoredUsers.clear();
        if (document.containsKey("ignoredUsers")) {
            List<Document> ignoredUsers = document.getList("ignoredUsers", Document.class);

            ignoredUsers.forEach(ignoredUser -> this.ignoredUsers.add(new SimpleUser(ignoredUser)));
        }

        if (document.containsKey("chatType")) {
            this.chatType = ChatType.valueOf(document.getString("chatType"));
        }

        if (document.containsKey("tradeIgnore")) {
            this.tradeIgnore = document.getBoolean("tradeIgnore");
        }

        this.notes.clear();
        if (document.containsKey("notes")) {
            List<Document> notesDocuments = document.getList("notes", Document.class);
            notes.addAll(notesDocuments.stream().map(Note::new).toList());
        }

        this.activesTags.clear();
        if (document.containsKey("activeTags")) {
            List<Document> activeTags = document.getList("activeTags", Document.class);

            activeTags.forEach(tagDocument ->
                    this.activesTags.put(
                            new ServerContext(tagDocument.get("serverContext", Document.class)), tagDocument.getString("tagName")));
        }

        this.ownedTags.clear();
        if (document.containsKey("ownedTags")) {
            this.ownedTags.addAll(document.getList("ownedTags", String.class));
        }

        this.ownedTags.clear();
        if (document.containsKey("trackedQueues")) {
            this.trackedQueues.addAll(document.getList("trackedQueues", String.class).stream().map(UUID::fromString).toList());
        }

        // Logs
        if (document.containsKey("reportLogs"))
            this.reportLogs = document.getList("reportLogs", Document.class).stream().map(ReportLog::new).collect(Collectors.toList());

        this.cooldows.clear();
        if (document.containsKey("cooldowns")) {
            Document cooldownsDocument = document.get("cooldowns", Document.class);

            for (Map.Entry<String, Object> entry : cooldownsDocument.entrySet()) {
                this.cooldows.put(entry.getKey(), ServiceManager.GSON.fromJson((String) entry.getValue(), Cooldown.class));
            }
        }

        if (document.containsKey("statistics")) {
            this.statistics = new ProfileStatistics(document.get("statistics", Document.class));
        }

        if (document.containsKey("discordId")) {
            this.discordId = document.getString("discordId");
        }

        this.dataLoaded = true;
    }

    public void loadMetadata(Document document, ServiceManager serviceManager) {
        serverMetadata.clear();
        //Load server meta
        if (document.containsKey("serverMetadata")) {
            Document serverMetaDocument = document.get("serverMetadata", Document.class);

            serverMetaDocument.forEach((serverName, metaMap) -> {
                HashMap<String, Document> serverSerializedMeta = new HashMap<>();

                // Only initialize current server metadata
                if (this.serverName != null && this.serverName.equals(serverName)) {
                    HashMap<Class<?>, ProfileServerMetadata> serverMeta = new HashMap<>();
                    Document serverData = serverMetaDocument.get(serverName, Document.class);

                    for (String clazz : serverData.keySet()) {
                        Document serializedMeta = serverData.get(clazz, Document.class);
                        serverSerializedMeta.put(clazz, serializedMeta);

                        try {
                            Class<?> aClass = Class.forName(clazz.replace("_", "."));
                            ProfileServerMetadata metadata = createProfileServerMetadata(aClass, serializedMeta, serviceManager);
                            serverMeta.put(aClass, metadata);
                        }  catch (ClassNotFoundException ignore) {}
                    }
                    this.serverMetadata.put(serverName, serverMeta);
                }

                this.serverSerializedMetadata.put(serverName, serverSerializedMeta);
            });
        }

        //Load global meta
        this.globalMetadata.clear();
        if (document.containsKey("metadata")) {
            Document globalMetaDocument = document.get("metadata", Document.class);

            globalMetaDocument.forEach((clazzName, ignore) -> {
                Document serializedMeta = globalMetaDocument.get(clazzName, Document.class);
                this.globalSerializedMetadata.put(clazzName, serializedMeta);
                try {
                    Class<?> aClass = Class.forName(clazzName.replace("_", "."));
                    globalMetadata.put(aClass, (ProfileMetadata) aClass.getDeclaredConstructor(Profile.class, Document.class).newInstance(this, serializedMeta));
                } catch (NoSuchMethodException | InstantiationException |
                         IllegalAccessException | ClassNotFoundException | InvocationTargetException | ClassCastException e) {
                    return;
                }
            });
        }

        this.metadataLoaded = true;
    }

    public void load(Document document, ServiceManager serviceManager) {
        this.loadData(document, serviceManager);
        this.loadMetadata(document, serviceManager);
    }

    private ProfileServerMetadata createProfileServerMetadata(Class<?> aClass, Document serializedMeta, ServiceManager serviceManager) {
        try {
            return (ProfileServerMetadata) aClass.getDeclaredConstructor(Profile.class, Document.class, ServiceManager.class)
                    .newInstance(this, serializedMeta, serviceManager);
        } catch (ReflectiveOperationException e1) {
            try {
                return (ProfileServerMetadata) aClass.getDeclaredConstructor(Profile.class, Document.class)
                        .newInstance(this, serializedMeta);
            } catch (ReflectiveOperationException e2) {
                logger.log(Level.SEVERE, "Failed to create ProfileServerMetadata for class: " + aClass.getName(), e2);
                return null;
            }
        }
    }


    public void loadFromOldDatabase(Document document, ServiceManager serviceManager) {
        this.toMigrate = document;

        MongoBackend mongoBackend = serviceManager.getMongoBackend();

        this.name = document.getString("name");
        this.lastAddress = document.getString("address");

        this.addresses.add(lastAddress);

        if (document.containsKey("identifiers")) {
            List<String> identifiers = document.getList("identifiers", String.class);

            addresses.addAll(identifiers);
        }

        if (document.containsKey("permissions")) {
            this.personalPermissions.addAll(document.getList("permissions", String.class));
        }

        mongoBackend.getDatabase(ProfileService.OLD_DATABASE_NAME) //why???
                .getCollection("grants")
                .find(Filters.eq("addedTo", uniqueId.toString()))
                .forEach((Consumer<? super Document>) grantDocument -> {
                    String addedReason = grantDocument.getString("addedReason");

                    String addedBy = grantDocument.getString("addedBy");

                    String rankName = grantDocument.getString("rankName");

                    String removedReason = "";
                    String removedBy = "";
                    long removedAt = 0;

                    if (grantDocument.containsKey("removedReason")) {
                        removedReason = grantDocument.getString("removedReason");
                        removedBy = grantDocument.getString("removedBy");
                        removedAt = grantDocument.get("removedAt", Number.class).longValue();
                    }

                    long addedAt = grantDocument.get("addedAt", Number.class).longValue();
                    long expiration = grantDocument.get("expiration", Number.class).longValue();

                    boolean expired = grantDocument.getBoolean("expired");
                    List<String> servers = grantDocument.getList("servers", String.class);

                    boolean permanent = grantDocument.getBoolean("permanent");
                    boolean global = grantDocument.getBoolean("global");

                    SimpleUser addedByUser = addedBy.equalsIgnoreCase("console")
                            ? SimpleUser.CONSOLE_USER :
                            new SimpleUser(serviceManager.getNamesService().getUniqueIdOrLookup(addedBy), addedBy);

                    ServerContext context = global ? ServerContext.GLOBAL :
                            ServerContext.build(servers);

                    Rank rank = serviceManager.getRankService().getRank(rankName);

                    if (rank == null) {
                        serviceManager.getLogger().severe("Rank '"+rankName+"' saved in old database doesn't exist!");
                        return;
                    }

                    Grant grant = new Grant(addedByUser, addedReason, context, rank);

                    grant.setAddedAt(addedAt);

                    long duration = expiration - addedAt;

                    if (permanent) {
                        grant.setDuration(-1);
                    } else {
                        grant.setDuration(duration);
                    }

                    if (expired) {
                        SimpleUser removedByUser = new SimpleUser(serviceManager.getNamesService().getUniqueIdOrLookup(removedBy), removedBy);
                        grant.setRemovedBy(removedByUser);
                        grant.setRemovedReason(removedReason);
                        grant.setRemovedAt(removedAt);
                    }

                    this.grants.add(grant);
                });

        updateActiveGrant(serviceManager);

        mongoBackend.getDatabase(ProfileService.OLD_DATABASE_NAME) //why??? x2
                .getCollection("punishments")
                .find(Filters.eq("targetID", uniqueId.toString()))
                .forEach((Consumer<? super Document>) punishmentDocument -> {
                    Punishment punishment = new Punishment();

                    String type = punishmentDocument.getString("type");
                    String reason = punishmentDocument.getString("addedReason");
                    long addedAt = punishmentDocument.get("addedAt", Number.class).longValue();

                    String staffName = punishmentDocument.getString("staffName");

                    String removedReason = null;
                    long removedAt = System.currentTimeMillis();

                    if (punishmentDocument.containsKey("removedReason")) {
                        removedReason = punishmentDocument.getString("removedReason");
                        removedAt = punishmentDocument.get("removedAt", Number.class).longValue();
                    }

                    long expiration = punishmentDocument.containsKey("expiration") ?
                            punishmentDocument.get("expiration", Number.class).longValue() : -1;

                    punishment.setType(PunishmentType.fromString(type));
                    punishment.setReason(reason);

                    punishment.setAddedAt(addedAt);

                    String victimName = punishmentDocument.getString("victimName");
                    UUID victimUniqueId = UUID.fromString(punishmentDocument.getString("targetID"));

                    SimpleUser target = new SimpleUser(victimUniqueId, victimName);
                    punishment.setTarget(target);

                    SimpleUser addedByUser = staffName.equalsIgnoreCase("console") ?
                            SimpleUser.CONSOLE_USER :
                            new SimpleUser(serviceManager.getNamesService().getUniqueIdOrLookup(staffName), staffName);

                    punishment.setAddedBy(addedByUser);

                    if (expiration > 0) {
                        punishment.setDuration(expiration);
                    } else {
                        punishment.setDuration(-1);
                    }

                    if (removedReason != null) {

                        punishment.setRemovedBy(SimpleUser.CONSOLE_USER);
                        punishment.setRemovedReason(removedReason);
                        punishment.setRemovedAt(removedAt);
                    }

                    punishment.setContext(ServerContext.GLOBAL);

                    punishments.add(punishment);
                });
    }

    public void updateActiveGrant(ServiceManager serviceManager) {
        RankService rankService = serviceManager.getRankService();
        ServersService serversService = serviceManager.getServersService();

        List<Grant> sortedGrants = grants.stream()
                .filter(grant -> !grant.isRemoved())
                .toList();

        sortedGrants.forEach(grant -> {

            if (grant.equals(activeGrant)) return;

            if (grant.hasExpired()) {
                grant.setRemovedAt(System.currentTimeMillis());
                grant.setRemovedBy(SimpleUser.CONSOLE_USER);
                grant.setRemovedReason("Expired");
                return;
            }

            if (!grant.getContext().applies(serversService.getCurrentServer())) {
                return;
            }

            if (activeGrant == null || activeGrant.isRemoved()) {
                activeGrant = grant;
                return;
            }

            if (activeGrant != null && !activeGrant.isRemoved()) {
                if (activeGrant.getRank().getWeight() < grant.getRank().getWeight()) {
                    return;
                }
            }

            activeGrant = grant;
        });

        if (activeGrant == null) {
            activeGrant = new Grant(SimpleUser.CONSOLE_USER, "default-rank");
            activeGrant.setRank(rankService.getDefaultRank());
            activeGrant.setDuration(-1);

            grants.add(activeGrant);
        }


        if (serviceManager.getSyncService() != null)
            serviceManager.getSyncService().sendRankSyncRequest(this);
    }

    public Grant getActiveGrantInContext(ServiceManager serviceManager, ServerContext context) {
        RankService rankService = serviceManager.getRankService();
        ServersService serversService = serviceManager.getServersService();

        List<Grant> sortedGrants = grants.stream()
                .filter(grant -> !grant.isRemoved())
                .toList();

        Grant activeGrant = null;

        for (Grant grant : sortedGrants) {
            if (grant.equals(activeGrant)) continue;

            if (grant.hasExpired()) {
                continue;
            }

            if (!grant.getContext().applies(serversService, context)) {
                continue;
            }

            if (activeGrant == null || activeGrant.isRemoved()) {
                activeGrant = grant;
                continue;
            }

            if (!activeGrant.isRemoved()) {
                if (activeGrant.getRank().getWeight() < grant.getRank().getWeight()) {
                    continue;
                }
            }

            activeGrant = grant;
        }

        if (activeGrant == null) {
            activeGrant = new Grant(SimpleUser.CONSOLE_USER, "default-rank");
            activeGrant.setRank(rankService.getDefaultRank());
            activeGrant.setDuration(-1);
        }

        return activeGrant;
    }

    public void grant(ServiceManager serviceManager, Grant grant) {
        if (serviceManager.getProfileService().getProfile(this.getUniqueId()) == null) {
            this.getGrants().add(grant);
        }
        serviceManager.getProfileService().saveProfile(this);
        serviceManager.getRedisBackend().sendPacket(new PlayerGrantPacket(this.getUniqueId(), grant));
    }

    public Document serialize(ServiceManager serviceManager) {
        PrismaServer currentServer = serviceManager.getServersService().getCurrentServer();

        Document document = new Document("uniqueId", uniqueId.toString())
                .append("name", name);

        document.append("globalFirstSeen", globalFirstSeen);

        //Server metadata
        Document serverListMetaDocument = new Document();
        this.serverSerializedMetadata.forEach((server, metaMap) -> {
            Document serverMetaMap = new Document();
            metaMap.forEach((clazz, serializedMeta) -> {
                String fieldName = clazz.replace(".", "_");
                try {
                    Class<?> aClass = Class.forName(fieldName.replace("_", "."));
                    ProfileServerMetadata meta = this.serverMetadata.getOrDefault(server, new HashMap<>()).get(aClass);
                    if (meta != null) {
                        serverMetaMap.put(fieldName, meta.serialize());
                        //serviceManager.getProfileService().updateServerMetadata(this, meta);
                        return;
                    }
                } catch (ClassNotFoundException ignore) {
                }

                serverMetaMap.put(fieldName, serializedMeta);
            });

            serverListMetaDocument.put(server, serverMetaMap);
        });

        document.append("serverMetadata", serverListMetaDocument);

        //Global metadata
        Document globalMetaDocument = new Document();
        globalSerializedMetadata.forEach((clazz, serializedMeta) -> {
            String fieldName = clazz.replace(".", "_");
            //Check if metadata is loaded
            try {
                Class<?> aClass = Class.forName(fieldName.replace("_", "."));
                ProfileMetadata meta = this.globalMetadata.get(aClass);
                if (meta != null) {
                    serviceManager.getProfileService().updateGlobalMetadata(this, meta);
                    globalMetaDocument.put(fieldName, meta.serialize());
                    return;
                }
            } catch (ClassNotFoundException ignore) {}

            globalMetaDocument.put(fieldName, serializedMeta);
        });

        document.append("metadata", globalMetaDocument);

        List<Document> grants = Lists.newArrayList();
        this.grants.forEach(grant -> grants.add(grant.serialize()));
        document.append("grants", grants);

        List<Document> punishments = Lists.newArrayList();
        this.punishments.forEach(punishment -> punishments.add(punishment.serialize()));
        document.append("punishments", punishments);

        document.append("lastSeen", globalLastSeen);

        if (lastServer != null) {
            document.append("lastServer", lastServer.getName());
        }

        if (lastAddress != null) {
            document.append("lastAddress", lastAddress);
        }

        document.append("addresses", addresses);

        document.append("personalPermissions", personalPermissions);

        if (!ignoredUsers.isEmpty()) {
            document.append("ignoredUsers", ignoredUsers.stream().map(SimpleUser::serialize).collect(Collectors.toList()));
        }

        document.append("chatType", chatType.name());
        document.append("tradeIgnore", tradeIgnore);

        document.append("notes", notes.stream().map(Note::serialize).toList());

        List<Document> activeTagsDocument = new ArrayList<>();

        this.activesTags.forEach((serverContext, tagName) -> {
            Document tagDocument = new Document();
            tagDocument.append("serverContext", serverContext.serialize());
            tagDocument.append("tagName", tagName);
            activeTagsDocument.add(tagDocument);
        });

        document.append("activeTags", activeTagsDocument);
        document.append("ownedTags", ownedTags);

        document.append("trackedQueues", trackedQueues.stream().map(UUID::toString).toList());

        document.append("reportLogs", reportLogs.stream().map(DocumentSerialized::serialize).toList());

        Document cooldownsDocument = new Document();
        for (Map.Entry<String, Cooldown> entry : this.cooldows.entrySet()) {
            cooldownsDocument.append(entry.getKey(), ServiceManager.GSON.toJson(entry.getValue()));
        }
        document.append("cooldowns", cooldownsDocument);

        document.append("statistics", statistics.serialize());

        if (discordId != null) {
            document.append("discordId", discordId);
        }

        return document;
    }

    public <T extends ProfileServerMetadata> T getServerMetadata(ServiceManager services, Class<T> clazz) {
        return this.getServerMetadata(services.getServersService(), clazz);
    }

    public <T extends ProfileServerMetadata> T getServerMetadata(ProfileService profileService, Class<T> clazz) {
        return this.getServerMetadata(profileService.getServersService(), clazz);
    }

    public <T extends ProfileServerMetadata> T getServerMetadata(ServersService serversService, Class<T> clazz) {
        return this.getServerMetadata(serversService.getCurrentServer().getName(), clazz);
    }

    public <T extends ProfileServerMetadata> T getServerMetadata(String serverName, Class<T> clazz) {
        if (!serverMetadata.containsKey(serverName) || !serverMetadata.get(serverName).containsKey(clazz)) {
            Map<Class<?>, ProfileServerMetadata> serverMap = serverMetadata.getOrDefault(serverName, new HashMap<>());
            try {
                ProfileServerMetadata metadata = clazz.getDeclaredConstructor(Profile.class).newInstance(this);
                serverMap.put(clazz, metadata);
                this.serverMetadata.put(serverName, serverMap);

                Map<String, Document> serializedMetadata = serverSerializedMetadata.getOrDefault(serverName, new HashMap<>());
                serializedMetadata.put(clazz.getName().replace(".", "_"), metadata.serialize());
                this.serverSerializedMetadata.put(serverName, serializedMetadata);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return clazz.cast(serverMetadata.get(serverName).get(clazz));
    }

    @Nullable
    public <T extends ProfileServerMetadata> T getServerMetadata(Class<T> clazz) {
        if (this.serverName == null || this.serverName.isEmpty()) return null;
        if (!serverMetadata.containsKey(serverName) || !serverMetadata.get(serverName).containsKey(clazz)) {
            Map<Class<?>, ProfileServerMetadata> serverMap = serverMetadata.getOrDefault(serverName, new HashMap<>());
            try {
                ProfileServerMetadata metadata = clazz.getDeclaredConstructor(Profile.class).newInstance(this);
                serverMap.put(clazz, metadata);
                this.serverMetadata.put(serverName, serverMap);

                Map<String, Document> serializedMetadata = serverSerializedMetadata.getOrDefault(serverName, new HashMap<>());
                serializedMetadata.put(clazz.getName().replace(".", "_"), metadata.serialize());
                this.serverSerializedMetadata.put(serverName, serializedMetadata);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return clazz.cast(serverMetadata.get(serverName).get(clazz));
    }

    public <T extends ProfileServerMetadata> T getServerMetadataByClassName(ServiceManager services, Class<T> clazz) {
        return this.getServerMetadataByClassName(services.getServersService(), clazz);
    }

    public <T extends ProfileServerMetadata> T getServerMetadataByClassName(ProfileService profileService, Class<T> clazz) {
        return this.getServerMetadataByClassName(profileService.getServersService(), clazz);
    }

    public <T extends ProfileServerMetadata> T getServerMetadataByClassName(ServersService serversService, Class<T> clazzName) {
        String serverName = serversService.getCurrentServer().getName();

        Class<?> clazz = null;

        boolean exists = serverMetadata.containsKey(serverName);
        if (exists) {
            Map<Class<?>, ProfileServerMetadata> map = serverMetadata.get(serverName);
            for (Class<?> aClass : map.keySet()) {
                if (aClass.getName().equals(clazzName.getName())) {
                    clazz = aClass;
                    break;
                }
            }

            if (clazz == null) {
                try {
                    clazz = Class.forName(clazzName.getName());
                } catch (ClassNotFoundException e) {
                    throw new RuntimeException(e);
                }
                exists = false;
            }
        }

        if (!exists) {
            //Check if class exists
            Map<Class<?>, ProfileServerMetadata> serverMap = serverMetadata.getOrDefault(serverName, new HashMap<>());
            try {
                ProfileServerMetadata metadata = (ProfileServerMetadata) clazz.getDeclaredConstructor(Profile.class).newInstance(this);
                serverMap.put(clazz, metadata);
                this.serverMetadata.put(serverName, serverMap);

                Map<String, Document> serializedMetadata = serverSerializedMetadata.getOrDefault(serverName, new HashMap<>());
                serializedMetadata.put(clazz.getName().replace(".", "_"), metadata.serialize());
                this.serverSerializedMetadata.put(serverName, serializedMetadata);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        ProfileServerMetadata metadata = serverMetadata.get(serverName).get(clazz);
        if (!clazzName.isInstance(metadata)) {
            try {
                metadata = clazzName.getDeclaredConstructor(Profile.class, Document.class).newInstance(this, metadata.serialize());
                serverMetadata.get(serverName).put(clazz, metadata);
            } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                     NoSuchMethodException e) {
                throw new RuntimeException(e);
            }
        }

        return (T) metadata;
    }

    public <T extends ProfileMetadata> T getGlobalMetadata(Class<T> clazz) {
        if (!globalMetadata.containsKey(clazz)) {
            try {
                ProfileMetadata metadata = clazz.getConstructor(Profile.class).newInstance(this);
                globalMetadata.put(clazz, metadata);
                globalSerializedMetadata.put(clazz.getName().replace(".", "_"), metadata.serialize());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        return clazz.cast(globalMetadata.get(clazz));
    }

    public <T extends ProfileMetadata> T getGlobalMetadataByClassName(Class<T> clazzName) {
        Class<?> clazz = null;
        for (Class<?> aClass : globalMetadata.keySet()) {
            if (aClass.getName().equals(clazzName.getName())) {
                clazz = aClass;
                break;
            }
        }

        if (clazz == null) {
            try {
                clazz = Class.forName(clazzName.getName());
            } catch (ClassNotFoundException e) {
                throw new RuntimeException(e);
            }
        }

        if (!globalMetadata.containsKey(clazz)) {
            try {
                ProfileMetadata metadata = (ProfileMetadata) clazz.getConstructor(Profile.class).newInstance(this);
                globalMetadata.put(clazz, metadata);
                globalSerializedMetadata.put(clazz.getName().replace(".", "_"), metadata.serialize());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        ProfileMetadata metadata = globalMetadata.get(clazz);
        if (!clazzName.isInstance(metadata)) {
            try {
                metadata = clazzName.getDeclaredConstructor(Profile.class, Document.class).newInstance(this, metadata.serialize());
                globalMetadata.put(clazz, metadata);
            } catch (InstantiationException | IllegalAccessException | InvocationTargetException |
                     NoSuchMethodException e) {
                throw new RuntimeException(e);
            }
        }

        return (T) metadata;
    }

    public boolean hasServerMetadata(ServiceManager services, Class<? extends ProfileServerMetadata> clazz) {
        String serverName = services.getServersService().getCurrentServer().getName();
        return serverMetadata.containsKey(serverName) && serverMetadata.get(serverName).containsKey(clazz);
    }

    public void registerServerMetadata(ServersService serversService, ProfileServerMetadata metadata) {
        String serverName = serversService.getCurrentServer().getName();
        Map<Class<?>, ProfileServerMetadata> serverMap = serverMetadata.getOrDefault(serverName, new HashMap<>());
        serverMap.put(metadata.getClass(), metadata);

        serverMetadata.put(serverName, serverMap);
    }

    public void unregisterServerMetadata(ServersService serversService, Class clazz) {
        String serverName = serversService.getCurrentServer().getName();
        Map<Class<?>, ProfileServerMetadata> serverMap = serverMetadata.get(serverName);
        if (serverMap != null) {
            serverMap.remove(clazz);
            serverMetadata.put(serverName, serverMap);
        }
    }

    public Rank getDisplayRank() {
        return this.isDisguised() ? this.disguiseRank : activeGrant.getRank();
    }

    public String getRealName() {
        return name;
    }

    public String getName() {
        return this.isDisguised() ? this.disguiseNick : name;
    }

    public String getDisplayName() {
        String nick = this.isDisguised() ? this.disguiseNick : name;
        Rank rank = this.isDisguised() ? this.disguiseRank : activeGrant.getRank();

        return rank.getPrefix() + nick + rank.getSuffix();
    }

    public String getRealDisplayName() {
        String nick = name;
        Rank rank = activeGrant.getRank();

        return rank.getPrefix()  + nick + rank.getSuffix();
    }

    public String getColoredName() {
        String nick = this.isDisguised() ? this.disguiseNick : name;
        Rank rank = this.isDisguised() ? this.disguiseRank : activeGrant.getRank();

        return (nickColor.isEmpty() ? (rank.getColor() == null ? "" : rank.getColor()) : nickColor) + nick;
    }

    public boolean isPunished() {
        return !this.getActivePunishmentsByType(
                PunishmentType.BAN,
                PunishmentType.BLACKLIST,
                PunishmentType.IP_BAN).isEmpty();
    }

    public Set<Punishment> getPunishmentsByType(PunishmentType... types) {
        return punishments.stream()
                .filter(punishment -> Arrays.asList(types).contains(punishment.getType()))
                .collect(Collectors.toSet());
    }

    public Set<Punishment> getActivePunishmentsByType(PrismaServer server, PunishmentType... types) {
        return getActivePunishments(punishment -> Arrays.asList(types).contains(punishment.getType()) && punishment.getContext() != null && punishment.getContext().applies(server));
    }

    public Set<Punishment> getActivePunishmentsByType(PunishmentType... types) {
        return getActivePunishments(punishment -> Arrays.asList(types).contains(punishment.getType()));
    }

    public Optional<Punishment> getActivePunishmentByType(PunishmentType type) {
        return getActivePunishmentsByType(type).stream().findFirst();
    }

    public Set<Punishment> getActivePunishments() {
        return punishments.stream()
                .filter(punishment -> punishment.isActive() && !punishment.isRemoved())
                .collect(Collectors.toSet());
    }

    public Set<Punishment> getActivePunishments(Predicate<Punishment> filter) {
        return punishments.stream()
                .filter(punishment -> filter.test(punishment) && punishment.isActive() && !punishment.isRemoved())
                .collect(Collectors.toSet());
    }

    public boolean isIgnoring(UUID uniqueId) {
        return ignoredUsers.stream().anyMatch(it -> it.getUniqueId().equals(uniqueId));
    }

    public boolean isIgnoring(String name) {
        return ignoredUsers.stream().anyMatch(it -> it.getName().equalsIgnoreCase(name));
    }

    public void addIgnoredUser(SimpleUser user) {
        ignoredUsers.add(user);
    }

    public void removeIgnoredUser(SimpleUser user) {
        ignoredUsers.remove(user);
    }

    public void removeIgnoredUser(UUID uniqueId) {
        ignoredUsers.removeIf(it -> it.getUniqueId().equals(uniqueId));
    }

    public void addPersonalPermission(String permission) {
        this.personalPermissions.add(permission);
    }

    public void removePersonalPermission(String permission) {
        this.personalPermissions.remove(permission);
    }

    @Override
    public boolean isDefaultLanguage() {
        return false;
    }

    public List<Grant> getActiveGrants() {
        return grants.stream().filter(g -> g != null && !g.isRemoved() && !g.hasExpired()).toList();
    }

    public List<String> getAllBukkitPermissions() {
        List<String> permissions = new ArrayList<>(this.personalPermissions);
        permissions.addAll(this.getActiveGrant().getRank().getAllPermissions());

        return permissions;
    }

    public List<String> getAllBukkitPermissionsForContext(ServiceManager services, ServerContext context) {
        Grant grant = this.getActiveGrant();
        if (!grant.getContext().applies(services.getServersService(), context)) {
            grant = this.getActiveGrantInContext(services, context);
        }
        List<String> permissions = new ArrayList<>(this.personalPermissions);
        permissions.addAll(grant.getRank().getAllPermissions());

        return permissions;
    }

    public boolean hasPermission(String permission) {
        if (permissionHandler == null) {
            return false;
        }
        return permissionHandler.hasPermission(permission);
    }

    public void removeAddress(String address) {
        this.getAddresses().remove(address);
        if (lastAddress != null && this.lastAddress.equalsIgnoreCase(address)) {
            this.lastAddress = null;
        }
    }

    public void setActiveTag(ServerContext serverContext, String tagName) {

        if (tagName == null) {
            activesTags.remove(serverContext);
            return;
        }

        activesTags.put(serverContext, tagName);
    }

    @Nullable
    public String getActiveTagName(@NotNull PrismaServer server) {
        return activesTags.entrySet().stream()
                .filter(entry -> entry.getKey().applies(server))
                .map(Map.Entry::getValue)
                .findFirst()
                .orElse(null);
    }

    @Nullable
    public String getActiveTagName(ServerContext serverContext) {
        return activesTags.get(serverContext);
    }

    public boolean canEquipTag(String tagName) {
        return this.ownedTags.contains(tagName);
    }

    public void addTag(String tagName) {
        this.ownedTags.add(tagName);
    }

    public void removeTag(String tagName) {
        this.ownedTags.remove(tagName);
    }

    public void addNote(Note note) {
        this.notes.add(note);
    }

    public void removeNote(Note note) {
        this.notes.remove(note);
    }

    public void setCooldown(String n, Duration duration) {
        cooldows.put(n, new Cooldown(duration));
    }

    public void setCooldown(String n, long duration) {
        cooldows.put(n, new Cooldown(duration));
    }

    public boolean hasCooldown(String n) {
        return cooldows.containsKey(n) && cooldows.get(n).isActive();
    }

    public Cooldown getCooldown(String n) {
        return cooldows.get(n);
    }

    public void removeCooldown(String n) {
        cooldows.remove(n);
    }

    public void addReportLog(ReportLog log) {
        this.reportLogs.add(log);

        reportLogs.sort((r1, r2) -> r2.getTime().compareTo(r1.getTime()));
    }

    public boolean hasDiscordSync() {
        return discordId != null;
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || this.getUniqueId() == null || getClass() != obj.getClass()) return false;

        Profile that = (Profile) obj;
        return this.getUniqueId().equals(that.getUniqueId());
    }
}
