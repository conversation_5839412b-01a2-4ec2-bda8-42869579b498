package org.contrum.prisma.profile.logs.reports;

import lombok.Getter;
import org.bson.Document;
import org.contrum.prisma.service.ServiceManager;
import org.contrum.prisma.utils.serialize.DocumentSerialized;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;

@Getter
public class ReportLog implements DocumentSerialized {

    private final String reporterName;
    private final String reason;

    private final String server;

    private final LocalDateTime time;

    public ReportLog(ServiceManager services, String by, String reason) {
        this.reporterName = by;
        this.reason = reason;

        this.server = services.getServersService().getCurrentServer().getName();

        this.time = LocalDateTime.now();
    }

    public ReportLog(Document document) {
        this.reporterName = document.getString("reporterName");
        this.reason = document.getString("reason");
        this.server = document.getString("server");

        this.time = ServiceManager.GSON.fromJson(document.getString("time"), LocalDateTime.class);
    }

    public String getFormattedDeathTime() {
        return time.format(DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss"));
    }

    @Override
    public Document serialize() {
        return new Document()
                .append("reporterName", reporterName)
                .append("reason", reason)
                .append("server", server)
                .append("time", ServiceManager.GSON.toJson(time));
    }
}
