/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by txmydev on 28/03/2024
 * Website: contrum.org
 */

package org.contrum.prisma.permissions;

public abstract class PermissionHandler {

    /**
     * Checks if the permission set has the specified permission.
     * This method is not yet implemented and will throw an UnsupportedOperationException when called.
     *
     * @param permission The permission to check for.
     * @return true if the permission set has the specified permission, false otherwise.
     * @throws UnsupportedOperationException if the method is not implemented.
     */
    public boolean hasPermission(String permission){
        throw new UnsupportedOperationException("Not implemented");
    }

    /**
     * Clears the permission set.
     * This is an abstract method that must be implemented by subclasses.
     */
    public abstract void clear();

    public abstract void update();

    public abstract void apply();
}
