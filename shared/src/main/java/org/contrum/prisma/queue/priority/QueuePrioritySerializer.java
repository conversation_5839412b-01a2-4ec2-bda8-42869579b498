package org.contrum.prisma.queue.priority;

import lombok.SneakyThrows;
import org.contrum.chorpu.configuration.serializer.Serializer;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class QueuePrioritySerializer implements Serializer<Object, List<QueuePriority>> {

    @Override
    public Map<String, Object> serialize(List<QueuePriority> queuePriority) {
        return null;
    }

    @SneakyThrows
    @Override
    public List<QueuePriority> deserialize(Object obj) {
        List<QueuePriority> priorities = new ArrayList<>();

        Class<?> aClass = Class.forName("org.bukkit.configuration.MemorySection");
        Map<String, Object> map;

        Method getMethod = aClass.getDeclaredMethod("get", String.class);
        Method valuesMethod = aClass.getDeclaredMethod("getValues", boolean.class);

        if (aClass.isInstance(obj)) {
            map = (Map<String, Object>) valuesMethod.invoke(obj, false);
        } else {
            return null;
        }

        for (Object value : map.values()) {
            String permission = (String) getMethod.invoke(value, "permission");
            int priority = ((Number) getMethod.invoke(value, "priority")).intValue();
            boolean bypass = (boolean) getMethod.invoke(value, "bypass");

            priorities.add(new QueuePriority(permission, priority, bypass));
        }

        return priorities;
    }
}