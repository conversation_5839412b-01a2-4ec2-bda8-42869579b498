package org.contrum.prisma.queue.redis.packet;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.bson.Document;
import org.contrum.prisma.utils.redis.pyrite.Packet;

import java.util.UUID;

@AllArgsConstructor
@NoArgsConstructor
@Getter
public class QueueUpdatePacket extends Packet {

    private UUID uuid;
    private Type type;
    private Document serializedQueue;


    public enum Type {
        CREATE,
        REMOVE,
        PAUSE,
        UPDATE_INTERVAL;
    }
}
