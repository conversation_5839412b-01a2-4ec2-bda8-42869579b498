package org.contrum.prisma.queue.redis;

import lombok.RequiredArgsConstructor;
import org.bson.Document;
import org.contrum.prisma.queue.Queue;
import org.contrum.prisma.queue.QueueService;
import org.contrum.prisma.queue.redis.packet.QueuePlayerUpdatePacket;
import org.contrum.prisma.queue.redis.packet.QueueTickPacket;
import org.contrum.prisma.queue.redis.packet.QueueUpdatePacket;
import org.contrum.prisma.service.ServiceManager;
import org.contrum.prisma.utils.redis.pyrite.packet.PacketContainer;
import org.contrum.prisma.utils.redis.pyrite.packet.RedisPacketListener;

import java.util.UUID;

@RequiredArgsConstructor
public class QueuePacketListener implements PacketContainer {

    private final ServiceManager serviceManager;
    private final QueueService queueService;

    @RedisPacketListener
    public void onQueueAddPlayerPacket(QueueUpdatePacket packet) {
        if (packet.getType().equals(QueueUpdatePacket.Type.REMOVE)){
            Queue queue = queueService.getQueue(packet.getUuid());
            if (queue != null) {
                queueService.getQueues().remove(queue);
            }
            return;
        }

        Document serialized = packet.getSerializedQueue();
        Queue data = new Queue(serviceManager, serialized);

        if (packet.getType().equals(QueueUpdatePacket.Type.CREATE)) {
            if (queueService.getQueueByServerName(data.getServer()) == null)
                queueService.getQueues().add(data);
            return;
        }

        Queue queue = queueService.getQueue(packet.getUuid());
        if (queue == null) return;

        switch (packet.getType()) {
            case PAUSE -> queue.setPaused(data.isPaused());
            case UPDATE_INTERVAL -> queue.setTickInterval(data.getTickInterval());
        }
    }

    @RedisPacketListener
    public void onPlayerUpdate(QueuePlayerUpdatePacket packet) {
        Queue.QueueUser user = packet.getUser();
        Queue queue = queueService.getQueue(packet.getQueueUUID());

        switch (packet.getType()) {
            case JOIN -> queue.addPlayer(user);
            case LEFT, SENT -> queue.removePlayer(user.getUuid());
        }
    }

    @RedisPacketListener
    public void onPlayerUpdate(QueueTickPacket packet) {
        Queue queue = queueService.getQueue(packet.getQueueUUID());

        queue.setLastTick(System.currentTimeMillis());
    }
}
