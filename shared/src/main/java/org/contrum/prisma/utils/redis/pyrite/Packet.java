/*
 * This file is part of the Apple Core project.
 * Copyright (c) 2022-2024. Contrum Services
 * Created by txmydev on 28/03/2024
 * Website: contrum.org
 */

package org.contrum.prisma.utils.redis.pyrite;

import lombok.Data;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;

/** Packet */
@Data
public class Packet {

    private final PacketMetadata metadata = new PacketMetadata(this);

    public static String getPacketIdentifier(Class<?> packetClass) {
        try {
            Method method = packetClass.getDeclaredMethod("getPacketIdentifier");

            return (String) method.invoke(null);
        } catch (NoSuchMethodException e) {
            return packetClass.getName();
        } catch (InvocationTargetException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
