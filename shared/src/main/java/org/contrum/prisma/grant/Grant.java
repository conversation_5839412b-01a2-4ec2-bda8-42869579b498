package org.contrum.prisma.grant;

import lombok.Getter;
import lombok.Setter;
import org.bson.Document;
import org.contrum.prisma.rank.Rank;
import org.contrum.prisma.rank.RankService;
import org.contrum.prisma.server.ServersService;
import org.contrum.prisma.utils.ServerContext;
import org.contrum.prisma.utils.user.SimpleUser;
import org.contrum.prisma.utils.uuid.UUIDUtils;
import org.contrum.prisma.utils.time.TimeUtils;
import org.contrum.prisma.utils.serialize.DocumentSerialized;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Date;
import java.util.UUID;

@Getter
@Setter
public class Grant implements DocumentSerialized {

    private final UUID id;

    private long addedAt;
    private long duration;

    private SimpleUser addedBy;
    private String addedReason;

    private Rank rank;

    private long removedAt;
    private SimpleUser removedBy;
    private String removedReason;

    private ServerContext context;

    public Grant(SimpleUser addedBy, String addedReason, ServerContext context, Duration duration, Rank rank) {
        this.id = UUID.randomUUID();
        this.addedAt = System.currentTimeMillis();

        this.rank = rank;

        this.addedBy = addedBy;
        this.addedReason = addedReason;
        if (duration.isNegative()) this.duration = -1;
        else this.duration = duration.toMillis();
        this.context = context;
    }

    public Grant(SimpleUser addedBy, String addedReason, ServerContext context, Rank rank) {
        this.id = UUID.randomUUID();
        this.addedAt = System.currentTimeMillis();

        this.rank = rank;

        this.addedBy = addedBy;
        this.addedReason = addedReason;
        this.context = context;
    }

    public Grant(SimpleUser addedBy, String addedReason) {
        this.id = UUID.randomUUID();
        this.addedAt = System.currentTimeMillis();

        this.addedBy = addedBy;
        this.addedReason = addedReason;
        this.context = ServerContext.GLOBAL;
    }

    public Grant(Document document, RankService rankService, ServersService serversService) {
        this.id = UUID.fromString(document.getString("id"));

        this.addedAt = document.get("addedAt", Number.class).longValue();
        this.duration = document.get("duration", Number.class).longValue();
        this.addedBy = new SimpleUser(document.get("addedBy", Document.class));
        this.addedReason = document.getString("addedReason");

        this.context = ServerContext.build(serversService, document.getString("context"));

        this.rank = rankService.getRank(document.getString("rank"));

        if (this.rank == null) {
            this.rank = rankService.getDefaultRank();
            this.removedAt = System.currentTimeMillis();
            this.removedReason = "Rank no longer exists";
            this.removedBy = SimpleUser.CONSOLE_USER;
        }

        if (document.containsKey("removedBy")) {
            this.removedBy = new SimpleUser(document.get("removedBy", Document.class));
            this.removedAt = document.get("removedAt", Number.class).longValue();
            this.removedReason = document.getString("removedReason");
        }
    }

    public boolean isRemoved() {
        return removedAt != 0;
    }

    public boolean isPermanent() {
        return duration == -1;
    }

    public boolean hasExpired() {

        if (removedReason != null && removedReason.equalsIgnoreCase("Expired")) return true;

        return (!isPermanent()) && (System.currentTimeMillis() >= addedAt + duration);
    }

    public Duration getRemainingTime() {
        if (this.isPermanent()) return Duration.ofSeconds(-1);

        return Duration.ofMillis((addedAt + duration) - System.currentTimeMillis());
    }

    public String getDurationFormatted() {
        if (isPermanent()) {
            return "Permanent";
        }

        return TimeUtils.getFormattedTime(duration);
    }

    public String getTimeLeftFormatted() {
        if (isPermanent()) {
            return "Never";
        }

        return TimeUtils.getFormattedTime(addedAt + duration - System.currentTimeMillis());
    }

    public long getTimeLeft() {
        if (isPermanent()) {
            return -1;
        }

        return addedAt + duration - System.currentTimeMillis();
    }

    @Override
    public Document serialize() {
        Document document = new Document();

        document.put("id", id.toString());
        document.put("addedAt", addedAt);
        document.put("duration", duration);
        document.put("addedBy", addedBy.serialize());
        document.put("addedReason", addedReason);
        document.put("context", context.toString());

        if (rank != null) {
            document.put("rank", rank.getName());
        }

        if (removedBy != null) {
            document.put("removedBy", removedBy.serialize());
        }

        if (removedAt != 0) {
            document.put("removedAt", removedAt);
        }

        if (removedReason != null) {
            document.put("removedReason", removedReason);
        }

        return document;
    }

    public String getAddedAtFormatted() {
        Date date = new Date(addedAt);
        SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");

        return format.format(date);
    }

    public String getExpiredAtFormatted() {
        Date date = new Date(addedAt + duration);
        SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");

        return format.format(date);
    }

    public String getRemovedAtFormatted() {
        if (!isRemoved()) {
            return "Never";
        }

        Date date = new Date(removedAt);
        SimpleDateFormat format = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");

        return format.format(date);
    }
}
